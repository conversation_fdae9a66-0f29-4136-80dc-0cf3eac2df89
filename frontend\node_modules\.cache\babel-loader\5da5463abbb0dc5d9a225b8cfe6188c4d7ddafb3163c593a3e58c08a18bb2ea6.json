{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\TrustVault\\\\frontend\\\\src\\\\pages\\\\Profile\\\\ProfilePage.tsx\",\n  _s = $RefreshSig$();\n// TrustVault - Profile Page\n\nimport React, { useState } from 'react';\nimport { Box, Typography, Card, CardContent, TextField, Button, Grid, Avatar, Divider, Alert } from '@mui/material';\nimport { Person, Security, Save, Lock } from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { useForm } from 'react-hook-form';\nimport { yupResolver } from '@hookform/resolvers/yup';\nimport * as yup from 'yup';\nimport { toast } from 'react-hot-toast';\n\n// Store\nimport { useAuthStore } from '../../store/authStore';\n\n// Components\nimport ChangePasswordModal from '../../components/Profile/ChangePasswordModal';\nimport MFASetupModal from '../../components/Profile/MFASetupModal';\n\n// Services\nimport apiService from '../../services/api';\n\n// Types\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n// Validation schema\nconst profileSchema = yup.object({\n  first_name: yup.string().min(2, 'First name must be at least 2 characters').required('First name is required'),\n  last_name: yup.string().min(2, 'Last name must be at least 2 characters').required('Last name is required'),\n  phone_number: yup.string().matches(/^\\+?[1-9]\\d{1,14}$/, 'Invalid phone number format').optional(),\n  timezone: yup.string().required('Timezone is required'),\n  language: yup.string().required('Language is required')\n});\nconst ProfilePage = () => {\n  _s();\n  var _errors$first_name, _errors$last_name, _errors$phone_number, _errors$timezone, _errors$language, _user$first_name, _user$last_name;\n  const {\n    user,\n    updateProfile,\n    isLoading,\n    loadUser\n  } = useAuthStore();\n  const [changePasswordModalOpen, setChangePasswordModalOpen] = useState(false);\n  const [mfaSetupModalOpen, setMfaSetupModalOpen] = useState(false);\n  const [disablingMFA, setDisablingMFA] = useState(false);\n  const {\n    register,\n    handleSubmit,\n    formState: {\n      errors,\n      isDirty\n    }\n  } = useForm({\n    resolver: yupResolver(profileSchema),\n    defaultValues: {\n      first_name: (user === null || user === void 0 ? void 0 : user.first_name) || '',\n      last_name: (user === null || user === void 0 ? void 0 : user.last_name) || '',\n      phone_number: (user === null || user === void 0 ? void 0 : user.phone_number) || '',\n      timezone: (user === null || user === void 0 ? void 0 : user.timezone) || 'UTC',\n      language: (user === null || user === void 0 ? void 0 : user.language) || 'en'\n    }\n  });\n  const onSubmit = async data => {\n    try {\n      await updateProfile(data);\n      toast.success('Profile updated successfully!');\n    } catch (error) {\n      toast.error('Failed to update profile');\n    }\n  };\n  const handleDisableMFA = async () => {\n    if (!window.confirm('Are you sure you want to disable two-factor authentication? This will make your account less secure.')) {\n      return;\n    }\n    setDisablingMFA(true);\n    try {\n      await apiService.disableMFA();\n      toast.success('2FA disabled successfully');\n      await loadUser(); // Refresh user data\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to disable 2FA';\n      toast.error(errorMessage);\n    } finally {\n      setDisablingMFA(false);\n    }\n  };\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(Typography, {\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"Profile - TrustVault\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: \"Manage your profile settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        mb: 4,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h1\",\n          gutterBottom: true,\n          children: \"Profile Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          color: \"text.secondary\",\n          children: \"Manage your personal information and account settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 2,\n                mb: 3,\n                children: [/*#__PURE__*/_jsxDEV(Person, {\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: \"Personal Information\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                component: \"form\",\n                onSubmit: handleSubmit(onSubmit),\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 3,\n                  children: [/*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    sm: 6,\n                    children: /*#__PURE__*/_jsxDEV(TextField, {\n                      ...register('first_name'),\n                      fullWidth: true,\n                      label: \"First Name\",\n                      error: !!errors.first_name,\n                      helperText: (_errors$first_name = errors.first_name) === null || _errors$first_name === void 0 ? void 0 : _errors$first_name.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 152,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 151,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    sm: 6,\n                    children: /*#__PURE__*/_jsxDEV(TextField, {\n                      ...register('last_name'),\n                      fullWidth: true,\n                      label: \"Last Name\",\n                      error: !!errors.last_name,\n                      helperText: (_errors$last_name = errors.last_name) === null || _errors$last_name === void 0 ? void 0 : _errors$last_name.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 162,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 161,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    children: /*#__PURE__*/_jsxDEV(TextField, {\n                      value: user.email,\n                      fullWidth: true,\n                      label: \"Email Address\",\n                      disabled: true,\n                      helperText: \"Email cannot be changed\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 172,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 171,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    children: /*#__PURE__*/_jsxDEV(TextField, {\n                      value: user.username,\n                      fullWidth: true,\n                      label: \"Username\",\n                      disabled: true,\n                      helperText: \"Username cannot be changed\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 182,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 181,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    children: /*#__PURE__*/_jsxDEV(TextField, {\n                      ...register('phone_number'),\n                      fullWidth: true,\n                      label: \"Phone Number\",\n                      type: \"tel\",\n                      error: !!errors.phone_number,\n                      helperText: (_errors$phone_number = errors.phone_number) === null || _errors$phone_number === void 0 ? void 0 : _errors$phone_number.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 192,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 191,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    sm: 6,\n                    children: /*#__PURE__*/_jsxDEV(TextField, {\n                      ...register('timezone'),\n                      fullWidth: true,\n                      label: \"Timezone\",\n                      select: true,\n                      SelectProps: {\n                        native: true\n                      },\n                      error: !!errors.timezone,\n                      helperText: (_errors$timezone = errors.timezone) === null || _errors$timezone === void 0 ? void 0 : _errors$timezone.message,\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"UTC\",\n                        children: \"UTC\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 212,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"America/New_York\",\n                        children: \"Eastern Time\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 213,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"America/Chicago\",\n                        children: \"Central Time\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 214,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"America/Denver\",\n                        children: \"Mountain Time\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 215,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"America/Los_Angeles\",\n                        children: \"Pacific Time\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 216,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"Europe/London\",\n                        children: \"London\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 217,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"Europe/Paris\",\n                        children: \"Paris\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 218,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"Asia/Tokyo\",\n                        children: \"Tokyo\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 219,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 203,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 202,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    sm: 6,\n                    children: /*#__PURE__*/_jsxDEV(TextField, {\n                      ...register('language'),\n                      fullWidth: true,\n                      label: \"Language\",\n                      select: true,\n                      SelectProps: {\n                        native: true\n                      },\n                      error: !!errors.language,\n                      helperText: (_errors$language = errors.language) === null || _errors$language === void 0 ? void 0 : _errors$language.message,\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"en\",\n                        children: \"English\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 233,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"fr\",\n                        children: \"Fran\\xE7ais\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 234,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"es\",\n                        children: \"Espa\\xF1ol\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 235,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"de\",\n                        children: \"Deutsch\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 236,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 224,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  mt: 3,\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    type: \"submit\",\n                    variant: \"contained\",\n                    startIcon: /*#__PURE__*/_jsxDEV(Save, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 245,\n                      columnNumber: 34\n                    }, this),\n                    disabled: !isDirty || isLoading,\n                    children: isLoading ? 'Saving...' : 'Save Changes'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              mb: 3\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                flexDirection: \"column\",\n                alignItems: \"center\",\n                textAlign: \"center\",\n                children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                  sx: {\n                    width: 80,\n                    height: 80,\n                    mb: 2,\n                    fontSize: '2rem'\n                  },\n                  children: [(_user$first_name = user.first_name) === null || _user$first_name === void 0 ? void 0 : _user$first_name[0], (_user$last_name = user.last_name) === null || _user$last_name === void 0 ? void 0 : _user$last_name[0]]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: [user.first_name, \" \", user.last_name]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  gutterBottom: true,\n                  children: user.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"text.secondary\",\n                  children: [\"Member since \", new Date(user.date_joined).toLocaleDateString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 2,\n                mb: 3,\n                children: [/*#__PURE__*/_jsxDEV(Security, {\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: \"Security\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                mb: 2,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  gutterBottom: true,\n                  children: \"Two-Factor Authentication\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Alert, {\n                  severity: user.is_mfa_enabled ? 'success' : 'warning',\n                  sx: {\n                    mb: 2\n                  },\n                  children: user.is_mfa_enabled ? 'Enabled' : 'Disabled'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 19\n                }, this), !user.is_mfa_enabled && /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  size: \"small\",\n                  fullWidth: true,\n                  sx: {\n                    mb: 2\n                  },\n                  onClick: () => setMfaSetupModalOpen(true),\n                  children: \"Enable 2FA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {\n                sx: {\n                  my: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  gutterBottom: true,\n                  children: \"Password\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"text.secondary\",\n                  display: \"block\",\n                  mb: 2,\n                  children: [\"Last changed: \", new Date(user.date_joined).toLocaleDateString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  size: \"small\",\n                  fullWidth: true,\n                  startIcon: /*#__PURE__*/_jsxDEV(Lock, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 334,\n                    columnNumber: 32\n                  }, this),\n                  onClick: () => setChangePasswordModalOpen(true),\n                  children: \"Change Password\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ChangePasswordModal, {\n      open: changePasswordModalOpen,\n      onClose: () => setChangePasswordModalOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 347,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MFASetupModal, {\n      open: mfaSetupModalOpen,\n      onClose: () => setMfaSetupModalOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 352,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(ProfilePage, \"3B+YOxFky8tDW3vWeczypO5fhUY=\", false, function () {\n  return [useAuthStore, useForm];\n});\n_c = ProfilePage;\nexport default ProfilePage;\nvar _c;\n$RefreshReg$(_c, \"ProfilePage\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "TextField", "<PERSON><PERSON>", "Grid", "Avatar", "Divider", "<PERSON><PERSON>", "Person", "Security", "Save", "Lock", "<PERSON><PERSON><PERSON>", "useForm", "yupResolver", "yup", "toast", "useAuthStore", "ChangePasswordModal", "MFASetupModal", "apiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "profileSchema", "object", "first_name", "string", "min", "required", "last_name", "phone_number", "matches", "optional", "timezone", "language", "ProfilePage", "_s", "_errors$first_name", "_errors$last_name", "_errors$phone_number", "_errors$timezone", "_errors$language", "_user$first_name", "_user$last_name", "user", "updateProfile", "isLoading", "loadUser", "changePasswordModalOpen", "setChangePasswordModalOpen", "mfaSetupModalOpen", "setMfaSetupModalOpen", "disablingMFA", "setDisablingMFA", "register", "handleSubmit", "formState", "errors", "isDirty", "resolver", "defaultValues", "onSubmit", "data", "success", "error", "handleDisableMFA", "window", "confirm", "disableMFA", "_error$response", "_error$response$data", "errorMessage", "response", "message", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "content", "mb", "variant", "component", "gutterBottom", "color", "container", "spacing", "item", "xs", "md", "display", "alignItems", "gap", "sm", "fullWidth", "label", "helperText", "value", "email", "disabled", "username", "type", "select", "SelectProps", "native", "mt", "startIcon", "sx", "flexDirection", "textAlign", "width", "height", "fontSize", "Date", "date_joined", "toLocaleDateString", "severity", "is_mfa_enabled", "size", "onClick", "my", "open", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/TrustVault/frontend/src/pages/Profile/ProfilePage.tsx"], "sourcesContent": ["// TrustVault - Profile Page\n\nimport React, { useState } from 'react';\nimport {\n  <PERSON>,\n  Typography,\n  Card,\n  CardContent,\n  TextField,\n  Button,\n  Grid,\n  Avatar,\n  Divider,\n  Alert,\n} from '@mui/material';\nimport {\n  Person,\n  Security,\n  Save,\n  Lock,\n} from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { useForm } from 'react-hook-form';\nimport { yupResolver } from '@hookform/resolvers/yup';\nimport * as yup from 'yup';\nimport { toast } from 'react-hot-toast';\n\n// Store\nimport { useAuthStore } from '../../store/authStore';\n\n// Components\nimport ChangePasswordModal from '../../components/Profile/ChangePasswordModal';\nimport MFASetupModal from '../../components/Profile/MFASetupModal';\n\n// Services\nimport apiService from '../../services/api';\n\n// Types\nimport { User } from '../../types';\n\ninterface ProfileFormData {\n  first_name: string;\n  last_name: string;\n  phone_number?: string;\n  timezone: string;\n  language: string;\n}\n\n// Validation schema\nconst profileSchema = yup.object({\n  first_name: yup\n    .string()\n    .min(2, 'First name must be at least 2 characters')\n    .required('First name is required'),\n  last_name: yup\n    .string()\n    .min(2, 'Last name must be at least 2 characters')\n    .required('Last name is required'),\n  phone_number: yup\n    .string()\n    .matches(/^\\+?[1-9]\\d{1,14}$/, 'Invalid phone number format')\n    .optional(),\n  timezone: yup.string().required('Timezone is required'),\n  language: yup.string().required('Language is required'),\n});\n\nconst ProfilePage: React.FC = () => {\n  const { user, updateProfile, isLoading, loadUser } = useAuthStore();\n  const [changePasswordModalOpen, setChangePasswordModalOpen] = useState(false);\n  const [mfaSetupModalOpen, setMfaSetupModalOpen] = useState(false);\n  const [disablingMFA, setDisablingMFA] = useState(false);\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors, isDirty },\n  } = useForm<ProfileFormData>({\n    resolver: yupResolver(profileSchema),\n    defaultValues: {\n      first_name: user?.first_name || '',\n      last_name: user?.last_name || '',\n      phone_number: user?.phone_number || '',\n      timezone: user?.timezone || 'UTC',\n      language: user?.language || 'en',\n    },\n  });\n\n  const onSubmit = async (data: ProfileFormData) => {\n    try {\n      await updateProfile(data);\n      toast.success('Profile updated successfully!');\n    } catch (error) {\n      toast.error('Failed to update profile');\n    }\n  };\n\n  const handleDisableMFA = async () => {\n    if (!window.confirm('Are you sure you want to disable two-factor authentication? This will make your account less secure.')) {\n      return;\n    }\n\n    setDisablingMFA(true);\n    try {\n      await apiService.disableMFA();\n      toast.success('2FA disabled successfully');\n      await loadUser(); // Refresh user data\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.message || 'Failed to disable 2FA';\n      toast.error(errorMessage);\n    } finally {\n      setDisablingMFA(false);\n    }\n  };\n\n  if (!user) {\n    return <Typography>Loading...</Typography>;\n  }\n\n  return (\n    <>\n      <Helmet>\n        <title>Profile - TrustVault</title>\n        <meta name=\"description\" content=\"Manage your profile settings\" />\n      </Helmet>\n\n      <Box>\n        {/* Header */}\n        <Box mb={4}>\n          <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n            Profile Settings\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            Manage your personal information and account settings\n          </Typography>\n        </Box>\n\n        <Grid container spacing={3}>\n          {/* Profile Information */}\n          <Grid item xs={12} md={8}>\n            <Card>\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                  <Person color=\"primary\" />\n                  <Typography variant=\"h6\">\n                    Personal Information\n                  </Typography>\n                </Box>\n\n                <Box component=\"form\" onSubmit={handleSubmit(onSubmit)}>\n                  <Grid container spacing={3}>\n                    <Grid item xs={12} sm={6}>\n                      <TextField\n                        {...register('first_name')}\n                        fullWidth\n                        label=\"First Name\"\n                        error={!!errors.first_name}\n                        helperText={errors.first_name?.message}\n                      />\n                    </Grid>\n\n                    <Grid item xs={12} sm={6}>\n                      <TextField\n                        {...register('last_name')}\n                        fullWidth\n                        label=\"Last Name\"\n                        error={!!errors.last_name}\n                        helperText={errors.last_name?.message}\n                      />\n                    </Grid>\n\n                    <Grid item xs={12}>\n                      <TextField\n                        value={user.email}\n                        fullWidth\n                        label=\"Email Address\"\n                        disabled\n                        helperText=\"Email cannot be changed\"\n                      />\n                    </Grid>\n\n                    <Grid item xs={12}>\n                      <TextField\n                        value={user.username}\n                        fullWidth\n                        label=\"Username\"\n                        disabled\n                        helperText=\"Username cannot be changed\"\n                      />\n                    </Grid>\n\n                    <Grid item xs={12}>\n                      <TextField\n                        {...register('phone_number')}\n                        fullWidth\n                        label=\"Phone Number\"\n                        type=\"tel\"\n                        error={!!errors.phone_number}\n                        helperText={errors.phone_number?.message}\n                      />\n                    </Grid>\n\n                    <Grid item xs={12} sm={6}>\n                      <TextField\n                        {...register('timezone')}\n                        fullWidth\n                        label=\"Timezone\"\n                        select\n                        SelectProps={{ native: true }}\n                        error={!!errors.timezone}\n                        helperText={errors.timezone?.message}\n                      >\n                        <option value=\"UTC\">UTC</option>\n                        <option value=\"America/New_York\">Eastern Time</option>\n                        <option value=\"America/Chicago\">Central Time</option>\n                        <option value=\"America/Denver\">Mountain Time</option>\n                        <option value=\"America/Los_Angeles\">Pacific Time</option>\n                        <option value=\"Europe/London\">London</option>\n                        <option value=\"Europe/Paris\">Paris</option>\n                        <option value=\"Asia/Tokyo\">Tokyo</option>\n                      </TextField>\n                    </Grid>\n\n                    <Grid item xs={12} sm={6}>\n                      <TextField\n                        {...register('language')}\n                        fullWidth\n                        label=\"Language\"\n                        select\n                        SelectProps={{ native: true }}\n                        error={!!errors.language}\n                        helperText={errors.language?.message}\n                      >\n                        <option value=\"en\">English</option>\n                        <option value=\"fr\">Français</option>\n                        <option value=\"es\">Español</option>\n                        <option value=\"de\">Deutsch</option>\n                      </TextField>\n                    </Grid>\n                  </Grid>\n\n                  <Box mt={3}>\n                    <Button\n                      type=\"submit\"\n                      variant=\"contained\"\n                      startIcon={<Save />}\n                      disabled={!isDirty || isLoading}\n                    >\n                      {isLoading ? 'Saving...' : 'Save Changes'}\n                    </Button>\n                  </Box>\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n\n          {/* Profile Summary & Security */}\n          <Grid item xs={12} md={4}>\n            {/* Profile Summary */}\n            <Card sx={{ mb: 3 }}>\n              <CardContent>\n                <Box display=\"flex\" flexDirection=\"column\" alignItems=\"center\" textAlign=\"center\">\n                  <Avatar\n                    sx={{\n                      width: 80,\n                      height: 80,\n                      mb: 2,\n                      fontSize: '2rem',\n                    }}\n                  >\n                    {user.first_name?.[0]}{user.last_name?.[0]}\n                  </Avatar>\n                  \n                  <Typography variant=\"h6\" gutterBottom>\n                    {user.first_name} {user.last_name}\n                  </Typography>\n                  \n                  <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                    {user.email}\n                  </Typography>\n                  \n                  <Typography variant=\"caption\" color=\"text.secondary\">\n                    Member since {new Date(user.date_joined).toLocaleDateString()}\n                  </Typography>\n                </Box>\n              </CardContent>\n            </Card>\n\n            {/* Security Settings */}\n            <Card>\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                  <Security color=\"primary\" />\n                  <Typography variant=\"h6\">\n                    Security\n                  </Typography>\n                </Box>\n\n                <Box mb={2}>\n                  <Typography variant=\"body2\" gutterBottom>\n                    Two-Factor Authentication\n                  </Typography>\n                  <Alert\n                    severity={user.is_mfa_enabled ? 'success' : 'warning'}\n                    sx={{ mb: 2 }}\n                  >\n                    {user.is_mfa_enabled ? 'Enabled' : 'Disabled'}\n                  </Alert>\n                  {!user.is_mfa_enabled && (\n                    <Button\n                      variant=\"outlined\"\n                      size=\"small\"\n                      fullWidth\n                      sx={{ mb: 2 }}\n                      onClick={() => setMfaSetupModalOpen(true)}\n                    >\n                      Enable 2FA\n                    </Button>\n                  )}\n                </Box>\n\n                <Divider sx={{ my: 2 }} />\n\n                <Box>\n                  <Typography variant=\"body2\" gutterBottom>\n                    Password\n                  </Typography>\n                  <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\" mb={2}>\n                    Last changed: {new Date(user.date_joined).toLocaleDateString()}\n                  </Typography>\n                  <Button\n                    variant=\"outlined\"\n                    size=\"small\"\n                    fullWidth\n                    startIcon={<Lock />}\n                    onClick={() => setChangePasswordModalOpen(true)}\n                  >\n                    Change Password\n                  </Button>\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n        </Grid>\n      </Box>\n\n      {/* Modals */}\n      <ChangePasswordModal\n        open={changePasswordModalOpen}\n        onClose={() => setChangePasswordModalOpen(false)}\n      />\n\n      <MFASetupModal\n        open={mfaSetupModalOpen}\n        onClose={() => setMfaSetupModalOpen(false)}\n      />\n    </>\n  );\n};\n\nexport default ProfilePage;\n"], "mappings": ";;AAAA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,MAAM,EACNC,OAAO,EACPC,KAAK,QACA,eAAe;AACtB,SACEC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,IAAI,QACC,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,WAAW,QAAQ,yBAAyB;AACrD,OAAO,KAAKC,GAAG,MAAM,KAAK;AAC1B,SAASC,KAAK,QAAQ,iBAAiB;;AAEvC;AACA,SAASC,YAAY,QAAQ,uBAAuB;;AAEpD;AACA,OAAOC,mBAAmB,MAAM,8CAA8C;AAC9E,OAAOC,aAAa,MAAM,wCAAwC;;AAElE;AACA,OAAOC,UAAU,MAAM,oBAAoB;;AAE3C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAWA;AACA,MAAMC,aAAa,GAAGV,GAAG,CAACW,MAAM,CAAC;EAC/BC,UAAU,EAAEZ,GAAG,CACZa,MAAM,CAAC,CAAC,CACRC,GAAG,CAAC,CAAC,EAAE,0CAA0C,CAAC,CAClDC,QAAQ,CAAC,wBAAwB,CAAC;EACrCC,SAAS,EAAEhB,GAAG,CACXa,MAAM,CAAC,CAAC,CACRC,GAAG,CAAC,CAAC,EAAE,yCAAyC,CAAC,CACjDC,QAAQ,CAAC,uBAAuB,CAAC;EACpCE,YAAY,EAAEjB,GAAG,CACda,MAAM,CAAC,CAAC,CACRK,OAAO,CAAC,oBAAoB,EAAE,6BAA6B,CAAC,CAC5DC,QAAQ,CAAC,CAAC;EACbC,QAAQ,EAAEpB,GAAG,CAACa,MAAM,CAAC,CAAC,CAACE,QAAQ,CAAC,sBAAsB,CAAC;EACvDM,QAAQ,EAAErB,GAAG,CAACa,MAAM,CAAC,CAAC,CAACE,QAAQ,CAAC,sBAAsB;AACxD,CAAC,CAAC;AAEF,MAAMO,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,kBAAA,EAAAC,iBAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,eAAA;EAClC,MAAM;IAAEC,IAAI;IAAEC,aAAa;IAAEC,SAAS;IAAEC;EAAS,CAAC,GAAGhC,YAAY,CAAC,CAAC;EACnE,MAAM,CAACiC,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EAC7E,MAAM,CAACuD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACyD,YAAY,EAAEC,eAAe,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAM;IACJ2D,QAAQ;IACRC,YAAY;IACZC,SAAS,EAAE;MAAEC,MAAM;MAAEC;IAAQ;EAC/B,CAAC,GAAG/C,OAAO,CAAkB;IAC3BgD,QAAQ,EAAE/C,WAAW,CAACW,aAAa,CAAC;IACpCqC,aAAa,EAAE;MACbnC,UAAU,EAAE,CAAAmB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEnB,UAAU,KAAI,EAAE;MAClCI,SAAS,EAAE,CAAAe,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEf,SAAS,KAAI,EAAE;MAChCC,YAAY,EAAE,CAAAc,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEd,YAAY,KAAI,EAAE;MACtCG,QAAQ,EAAE,CAAAW,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEX,QAAQ,KAAI,KAAK;MACjCC,QAAQ,EAAE,CAAAU,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEV,QAAQ,KAAI;IAC9B;EACF,CAAC,CAAC;EAEF,MAAM2B,QAAQ,GAAG,MAAOC,IAAqB,IAAK;IAChD,IAAI;MACF,MAAMjB,aAAa,CAACiB,IAAI,CAAC;MACzBhD,KAAK,CAACiD,OAAO,CAAC,+BAA+B,CAAC;IAChD,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdlD,KAAK,CAACkD,KAAK,CAAC,0BAA0B,CAAC;IACzC;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,sGAAsG,CAAC,EAAE;MAC3H;IACF;IAEAd,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF,MAAMnC,UAAU,CAACkD,UAAU,CAAC,CAAC;MAC7BtD,KAAK,CAACiD,OAAO,CAAC,2BAA2B,CAAC;MAC1C,MAAMhB,QAAQ,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,OAAOiB,KAAU,EAAE;MAAA,IAAAK,eAAA,EAAAC,oBAAA;MACnB,MAAMC,YAAY,GAAG,EAAAF,eAAA,GAAAL,KAAK,CAACQ,QAAQ,cAAAH,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBP,IAAI,cAAAQ,oBAAA,uBAApBA,oBAAA,CAAsBG,OAAO,KAAI,uBAAuB;MAC7E3D,KAAK,CAACkD,KAAK,CAACO,YAAY,CAAC;IAC3B,CAAC,SAAS;MACRlB,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,IAAI,CAACT,IAAI,EAAE;IACT,oBAAOxB,OAAA,CAACvB,UAAU;MAAA6E,QAAA,EAAC;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC;EAC5C;EAEA,oBACE1D,OAAA,CAAAE,SAAA;IAAAoD,QAAA,gBACEtD,OAAA,CAACV,MAAM;MAAAgE,QAAA,gBACLtD,OAAA;QAAAsD,QAAA,EAAO;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACnC1D,OAAA;QAAM2D,IAAI,EAAC,aAAa;QAACC,OAAO,EAAC;MAA8B;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5D,CAAC,eAET1D,OAAA,CAACxB,GAAG;MAAA8E,QAAA,gBAEFtD,OAAA,CAACxB,GAAG;QAACqF,EAAE,EAAE,CAAE;QAAAP,QAAA,gBACTtD,OAAA,CAACvB,UAAU;UAACqF,OAAO,EAAC,IAAI;UAACC,SAAS,EAAC,IAAI;UAACC,YAAY;UAAAV,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb1D,OAAA,CAACvB,UAAU;UAACqF,OAAO,EAAC,OAAO;UAACG,KAAK,EAAC,gBAAgB;UAAAX,QAAA,EAAC;QAEnD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEN1D,OAAA,CAAClB,IAAI;QAACoF,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAb,QAAA,gBAEzBtD,OAAA,CAAClB,IAAI;UAACsF,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eACvBtD,OAAA,CAACtB,IAAI;YAAA4E,QAAA,eACHtD,OAAA,CAACrB,WAAW;cAAA2E,QAAA,gBACVtD,OAAA,CAACxB,GAAG;gBAAC+F,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,GAAG,EAAE,CAAE;gBAACZ,EAAE,EAAE,CAAE;gBAAAP,QAAA,gBACpDtD,OAAA,CAACd,MAAM;kBAAC+E,KAAK,EAAC;gBAAS;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC1B1D,OAAA,CAACvB,UAAU;kBAACqF,OAAO,EAAC,IAAI;kBAAAR,QAAA,EAAC;gBAEzB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAEN1D,OAAA,CAACxB,GAAG;gBAACuF,SAAS,EAAC,MAAM;gBAACtB,QAAQ,EAAEN,YAAY,CAACM,QAAQ,CAAE;gBAAAa,QAAA,gBACrDtD,OAAA,CAAClB,IAAI;kBAACoF,SAAS;kBAACC,OAAO,EAAE,CAAE;kBAAAb,QAAA,gBACzBtD,OAAA,CAAClB,IAAI;oBAACsF,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACK,EAAE,EAAE,CAAE;oBAAApB,QAAA,eACvBtD,OAAA,CAACpB,SAAS;sBAAA,GACJsD,QAAQ,CAAC,YAAY,CAAC;sBAC1ByC,SAAS;sBACTC,KAAK,EAAC,YAAY;sBAClBhC,KAAK,EAAE,CAAC,CAACP,MAAM,CAAChC,UAAW;sBAC3BwE,UAAU,GAAA5D,kBAAA,GAAEoB,MAAM,CAAChC,UAAU,cAAAY,kBAAA,uBAAjBA,kBAAA,CAAmBoC;oBAAQ;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eAEP1D,OAAA,CAAClB,IAAI;oBAACsF,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACK,EAAE,EAAE,CAAE;oBAAApB,QAAA,eACvBtD,OAAA,CAACpB,SAAS;sBAAA,GACJsD,QAAQ,CAAC,WAAW,CAAC;sBACzByC,SAAS;sBACTC,KAAK,EAAC,WAAW;sBACjBhC,KAAK,EAAE,CAAC,CAACP,MAAM,CAAC5B,SAAU;sBAC1BoE,UAAU,GAAA3D,iBAAA,GAAEmB,MAAM,CAAC5B,SAAS,cAAAS,iBAAA,uBAAhBA,iBAAA,CAAkBmC;oBAAQ;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eAEP1D,OAAA,CAAClB,IAAI;oBAACsF,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAAAf,QAAA,eAChBtD,OAAA,CAACpB,SAAS;sBACRkG,KAAK,EAAEtD,IAAI,CAACuD,KAAM;sBAClBJ,SAAS;sBACTC,KAAK,EAAC,eAAe;sBACrBI,QAAQ;sBACRH,UAAU,EAAC;oBAAyB;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eAEP1D,OAAA,CAAClB,IAAI;oBAACsF,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAAAf,QAAA,eAChBtD,OAAA,CAACpB,SAAS;sBACRkG,KAAK,EAAEtD,IAAI,CAACyD,QAAS;sBACrBN,SAAS;sBACTC,KAAK,EAAC,UAAU;sBAChBI,QAAQ;sBACRH,UAAU,EAAC;oBAA4B;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eAEP1D,OAAA,CAAClB,IAAI;oBAACsF,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAAAf,QAAA,eAChBtD,OAAA,CAACpB,SAAS;sBAAA,GACJsD,QAAQ,CAAC,cAAc,CAAC;sBAC5ByC,SAAS;sBACTC,KAAK,EAAC,cAAc;sBACpBM,IAAI,EAAC,KAAK;sBACVtC,KAAK,EAAE,CAAC,CAACP,MAAM,CAAC3B,YAAa;sBAC7BmE,UAAU,GAAA1D,oBAAA,GAAEkB,MAAM,CAAC3B,YAAY,cAAAS,oBAAA,uBAAnBA,oBAAA,CAAqBkC;oBAAQ;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eAEP1D,OAAA,CAAClB,IAAI;oBAACsF,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACK,EAAE,EAAE,CAAE;oBAAApB,QAAA,eACvBtD,OAAA,CAACpB,SAAS;sBAAA,GACJsD,QAAQ,CAAC,UAAU,CAAC;sBACxByC,SAAS;sBACTC,KAAK,EAAC,UAAU;sBAChBO,MAAM;sBACNC,WAAW,EAAE;wBAAEC,MAAM,EAAE;sBAAK,CAAE;sBAC9BzC,KAAK,EAAE,CAAC,CAACP,MAAM,CAACxB,QAAS;sBACzBgE,UAAU,GAAAzD,gBAAA,GAAEiB,MAAM,CAACxB,QAAQ,cAAAO,gBAAA,uBAAfA,gBAAA,CAAiBiC,OAAQ;sBAAAC,QAAA,gBAErCtD,OAAA;wBAAQ8E,KAAK,EAAC,KAAK;wBAAAxB,QAAA,EAAC;sBAAG;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAChC1D,OAAA;wBAAQ8E,KAAK,EAAC,kBAAkB;wBAAAxB,QAAA,EAAC;sBAAY;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACtD1D,OAAA;wBAAQ8E,KAAK,EAAC,iBAAiB;wBAAAxB,QAAA,EAAC;sBAAY;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACrD1D,OAAA;wBAAQ8E,KAAK,EAAC,gBAAgB;wBAAAxB,QAAA,EAAC;sBAAa;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACrD1D,OAAA;wBAAQ8E,KAAK,EAAC,qBAAqB;wBAAAxB,QAAA,EAAC;sBAAY;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACzD1D,OAAA;wBAAQ8E,KAAK,EAAC,eAAe;wBAAAxB,QAAA,EAAC;sBAAM;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC7C1D,OAAA;wBAAQ8E,KAAK,EAAC,cAAc;wBAAAxB,QAAA,EAAC;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC3C1D,OAAA;wBAAQ8E,KAAK,EAAC,YAAY;wBAAAxB,QAAA,EAAC;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC,eAEP1D,OAAA,CAAClB,IAAI;oBAACsF,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACK,EAAE,EAAE,CAAE;oBAAApB,QAAA,eACvBtD,OAAA,CAACpB,SAAS;sBAAA,GACJsD,QAAQ,CAAC,UAAU,CAAC;sBACxByC,SAAS;sBACTC,KAAK,EAAC,UAAU;sBAChBO,MAAM;sBACNC,WAAW,EAAE;wBAAEC,MAAM,EAAE;sBAAK,CAAE;sBAC9BzC,KAAK,EAAE,CAAC,CAACP,MAAM,CAACvB,QAAS;sBACzB+D,UAAU,GAAAxD,gBAAA,GAAEgB,MAAM,CAACvB,QAAQ,cAAAO,gBAAA,uBAAfA,gBAAA,CAAiBgC,OAAQ;sBAAAC,QAAA,gBAErCtD,OAAA;wBAAQ8E,KAAK,EAAC,IAAI;wBAAAxB,QAAA,EAAC;sBAAO;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACnC1D,OAAA;wBAAQ8E,KAAK,EAAC,IAAI;wBAAAxB,QAAA,EAAC;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACpC1D,OAAA;wBAAQ8E,KAAK,EAAC,IAAI;wBAAAxB,QAAA,EAAC;sBAAO;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACnC1D,OAAA;wBAAQ8E,KAAK,EAAC,IAAI;wBAAAxB,QAAA,EAAC;sBAAO;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEP1D,OAAA,CAACxB,GAAG;kBAAC8G,EAAE,EAAE,CAAE;kBAAAhC,QAAA,eACTtD,OAAA,CAACnB,MAAM;oBACLqG,IAAI,EAAC,QAAQ;oBACbpB,OAAO,EAAC,WAAW;oBACnByB,SAAS,eAAEvF,OAAA,CAACZ,IAAI;sBAAAmE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACpBsB,QAAQ,EAAE,CAAC1C,OAAO,IAAIZ,SAAU;oBAAA4B,QAAA,EAE/B5B,SAAS,GAAG,WAAW,GAAG;kBAAc;oBAAA6B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGP1D,OAAA,CAAClB,IAAI;UAACsF,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,gBAEvBtD,OAAA,CAACtB,IAAI;YAAC8G,EAAE,EAAE;cAAE3B,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,eAClBtD,OAAA,CAACrB,WAAW;cAAA2E,QAAA,eACVtD,OAAA,CAACxB,GAAG;gBAAC+F,OAAO,EAAC,MAAM;gBAACkB,aAAa,EAAC,QAAQ;gBAACjB,UAAU,EAAC,QAAQ;gBAACkB,SAAS,EAAC,QAAQ;gBAAApC,QAAA,gBAC/EtD,OAAA,CAACjB,MAAM;kBACLyG,EAAE,EAAE;oBACFG,KAAK,EAAE,EAAE;oBACTC,MAAM,EAAE,EAAE;oBACV/B,EAAE,EAAE,CAAC;oBACLgC,QAAQ,EAAE;kBACZ,CAAE;kBAAAvC,QAAA,IAAAhC,gBAAA,GAEDE,IAAI,CAACnB,UAAU,cAAAiB,gBAAA,uBAAfA,gBAAA,CAAkB,CAAC,CAAC,GAAAC,eAAA,GAAEC,IAAI,CAACf,SAAS,cAAAc,eAAA,uBAAdA,eAAA,CAAiB,CAAC,CAAC;gBAAA;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,eAET1D,OAAA,CAACvB,UAAU;kBAACqF,OAAO,EAAC,IAAI;kBAACE,YAAY;kBAAAV,QAAA,GAClC9B,IAAI,CAACnB,UAAU,EAAC,GAAC,EAACmB,IAAI,CAACf,SAAS;gBAAA;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eAEb1D,OAAA,CAACvB,UAAU;kBAACqF,OAAO,EAAC,OAAO;kBAACG,KAAK,EAAC,gBAAgB;kBAACD,YAAY;kBAAAV,QAAA,EAC5D9B,IAAI,CAACuD;gBAAK;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAEb1D,OAAA,CAACvB,UAAU;kBAACqF,OAAO,EAAC,SAAS;kBAACG,KAAK,EAAC,gBAAgB;kBAAAX,QAAA,GAAC,eACtC,EAAC,IAAIwC,IAAI,CAACtE,IAAI,CAACuE,WAAW,CAAC,CAACC,kBAAkB,CAAC,CAAC;gBAAA;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAGP1D,OAAA,CAACtB,IAAI;YAAA4E,QAAA,eACHtD,OAAA,CAACrB,WAAW;cAAA2E,QAAA,gBACVtD,OAAA,CAACxB,GAAG;gBAAC+F,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,GAAG,EAAE,CAAE;gBAACZ,EAAE,EAAE,CAAE;gBAAAP,QAAA,gBACpDtD,OAAA,CAACb,QAAQ;kBAAC8E,KAAK,EAAC;gBAAS;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5B1D,OAAA,CAACvB,UAAU;kBAACqF,OAAO,EAAC,IAAI;kBAAAR,QAAA,EAAC;gBAEzB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAEN1D,OAAA,CAACxB,GAAG;gBAACqF,EAAE,EAAE,CAAE;gBAAAP,QAAA,gBACTtD,OAAA,CAACvB,UAAU;kBAACqF,OAAO,EAAC,OAAO;kBAACE,YAAY;kBAAAV,QAAA,EAAC;gBAEzC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb1D,OAAA,CAACf,KAAK;kBACJgH,QAAQ,EAAEzE,IAAI,CAAC0E,cAAc,GAAG,SAAS,GAAG,SAAU;kBACtDV,EAAE,EAAE;oBAAE3B,EAAE,EAAE;kBAAE,CAAE;kBAAAP,QAAA,EAEb9B,IAAI,CAAC0E,cAAc,GAAG,SAAS,GAAG;gBAAU;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,EACP,CAAClC,IAAI,CAAC0E,cAAc,iBACnBlG,OAAA,CAACnB,MAAM;kBACLiF,OAAO,EAAC,UAAU;kBAClBqC,IAAI,EAAC,OAAO;kBACZxB,SAAS;kBACTa,EAAE,EAAE;oBAAE3B,EAAE,EAAE;kBAAE,CAAE;kBACduC,OAAO,EAAEA,CAAA,KAAMrE,oBAAoB,CAAC,IAAI,CAAE;kBAAAuB,QAAA,EAC3C;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN1D,OAAA,CAAChB,OAAO;gBAACwG,EAAE,EAAE;kBAAEa,EAAE,EAAE;gBAAE;cAAE;gBAAA9C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAE1B1D,OAAA,CAACxB,GAAG;gBAAA8E,QAAA,gBACFtD,OAAA,CAACvB,UAAU;kBAACqF,OAAO,EAAC,OAAO;kBAACE,YAAY;kBAAAV,QAAA,EAAC;gBAEzC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb1D,OAAA,CAACvB,UAAU;kBAACqF,OAAO,EAAC,SAAS;kBAACG,KAAK,EAAC,gBAAgB;kBAACM,OAAO,EAAC,OAAO;kBAACV,EAAE,EAAE,CAAE;kBAAAP,QAAA,GAAC,gBAC5D,EAAC,IAAIwC,IAAI,CAACtE,IAAI,CAACuE,WAAW,CAAC,CAACC,kBAAkB,CAAC,CAAC;gBAAA;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC,eACb1D,OAAA,CAACnB,MAAM;kBACLiF,OAAO,EAAC,UAAU;kBAClBqC,IAAI,EAAC,OAAO;kBACZxB,SAAS;kBACTY,SAAS,eAAEvF,OAAA,CAACX,IAAI;oBAAAkE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACpB0C,OAAO,EAAEA,CAAA,KAAMvE,0BAA0B,CAAC,IAAI,CAAE;kBAAAyB,QAAA,EACjD;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGN1D,OAAA,CAACJ,mBAAmB;MAClB0G,IAAI,EAAE1E,uBAAwB;MAC9B2E,OAAO,EAAEA,CAAA,KAAM1E,0BAA0B,CAAC,KAAK;IAAE;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC,eAEF1D,OAAA,CAACH,aAAa;MACZyG,IAAI,EAAExE,iBAAkB;MACxByE,OAAO,EAAEA,CAAA,KAAMxE,oBAAoB,CAAC,KAAK;IAAE;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC;EAAA,eACF,CAAC;AAEP,CAAC;AAAC1C,EAAA,CAnSID,WAAqB;EAAA,QAC4BpB,YAAY,EAS7DJ,OAAO;AAAA;AAAAiH,EAAA,GAVPzF,WAAqB;AAqS3B,eAAeA,WAAW;AAAC,IAAAyF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}