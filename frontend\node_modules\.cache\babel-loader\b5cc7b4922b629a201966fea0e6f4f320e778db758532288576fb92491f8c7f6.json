{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\TrustVault\\\\frontend\\\\src\\\\pages\\\\Settings\\\\SettingsPage.tsx\",\n  _s = $RefreshSig$();\n// TrustVault - Settings Page\n\nimport React, { useState } from 'react';\nimport { Box, Typography, Grid, Card, CardContent, FormControl, InputLabel, Select, MenuItem, Switch, Button, Alert, List, ListItem, ListItemText, ListItemSecondaryAction, Dialog, DialogTitle, DialogContent, DialogActions, TextField } from '@mui/material';\nimport { Settings, Notifications, Security, Delete, Save } from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { toast } from 'react-hot-toast';\nimport { useMutation, useQueryClient, useQuery } from 'react-query';\n\n// Store\nimport { useAuthStore } from '../../store/authStore';\n\n// Contexts\nimport { useTheme } from '../../contexts/ThemeContext';\n\n// Services\nimport apiService from '../../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SettingsPage = () => {\n  _s();\n  const {\n    user\n  } = useAuthStore();\n  const queryClient = useQueryClient();\n  const {\n    themeMode,\n    setThemeMode,\n    isDarkMode\n  } = useTheme();\n  const [settings, setSettings] = useState({\n    timezone: (user === null || user === void 0 ? void 0 : user.timezone) || 'UTC',\n    language: (user === null || user === void 0 ? void 0 : user.language) || 'en',\n    theme: themeMode,\n    notifications: {\n      email: true,\n      push: true,\n      security: true,\n      portfolio: true\n    },\n    privacy: {\n      profile_visibility: 'private',\n      portfolio_visibility: 'private'\n    }\n  });\n  const [deleteAccountDialog, setDeleteAccountDialog] = useState(false);\n  const [deleteConfirmation, setDeleteConfirmation] = useState('');\n  const [sessionsDialog, setSessionsDialog] = useState(false);\n  const [exportingData, setExportingData] = useState(false);\n\n  // Load user settings\n  const {\n    data: userSettings,\n    isLoading: settingsLoading\n  } = useQuery('user-settings', apiService.getUserSettings, {\n    onSuccess: data => {\n      setSettings(prev => ({\n        ...prev,\n        ...data\n      }));\n    },\n    onError: () => {\n      // Use default settings if API fails\n    }\n  });\n\n  // Load active sessions\n  const {\n    data: activeSessions,\n    refetch: refetchSessions\n  } = useQuery('active-sessions', apiService.getActiveSessions, {\n    enabled: sessionsDialog,\n    onError: () => {\n      toast.error('Failed to load active sessions');\n    }\n  });\n  const updateSettingsMutation = useMutation(data => apiService.updateUserSettings(data), {\n    onSuccess: () => {\n      toast.success('Settings updated successfully');\n      queryClient.invalidateQueries(['user-settings', 'user-profile']);\n    },\n    onError: () => {\n      toast.error('Failed to update settings');\n    }\n  });\n  const deleteAccountMutation = useMutation(confirmation => apiService.deleteAccount(confirmation), {\n    onSuccess: () => {\n      toast.success('Account deleted successfully');\n      // Redirect to login or home page\n      window.location.href = '/';\n    },\n    onError: error => {\n      var _error$response, _error$response$data;\n      const message = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to delete account';\n      toast.error(message);\n    }\n  });\n  const revokeSessionMutation = useMutation(sessionId => apiService.revokeSession(sessionId), {\n    onSuccess: () => {\n      toast.success('Session revoked successfully');\n      refetchSessions();\n    },\n    onError: () => {\n      toast.error('Failed to revoke session');\n    }\n  });\n  const revokeAllSessionsMutation = useMutation(() => apiService.revokeAllSessions(), {\n    onSuccess: () => {\n      toast.success('All sessions revoked successfully');\n      refetchSessions();\n    },\n    onError: () => {\n      toast.error('Failed to revoke all sessions');\n    }\n  });\n  const handleSettingChange = (category, key, value) => {\n    setSettings(prev => ({\n      ...prev,\n      [category]: typeof prev[category] === 'object' && prev[category] !== null ? {\n        ...prev[category],\n        [key]: value\n      } : value\n    }));\n  };\n  const handleThemeChange = newTheme => {\n    setThemeMode(newTheme);\n    handleSettingChange('theme', '', newTheme);\n  };\n  const handleSaveSettings = () => {\n    updateSettingsMutation.mutate(settings);\n  };\n  const handleExportData = async () => {\n    setExportingData(true);\n    try {\n      const blob = await apiService.exportUserData();\n      const url = window.URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = `trustvault-data-${new Date().toISOString().split('T')[0]}.json`;\n      document.body.appendChild(a);\n      a.click();\n      window.URL.revokeObjectURL(url);\n      document.body.removeChild(a);\n      toast.success('Data exported successfully');\n    } catch (error) {\n      toast.error('Failed to export data');\n    } finally {\n      setExportingData(false);\n    }\n  };\n  const handleDeleteAccount = () => {\n    if (deleteConfirmation === 'DELETE') {\n      deleteAccountMutation.mutate(deleteConfirmation);\n    }\n  };\n  const timezones = [{\n    value: 'UTC',\n    label: 'UTC'\n  }, {\n    value: 'America/New_York',\n    label: 'Eastern Time'\n  }, {\n    value: 'America/Chicago',\n    label: 'Central Time'\n  }, {\n    value: 'America/Denver',\n    label: 'Mountain Time'\n  }, {\n    value: 'America/Los_Angeles',\n    label: 'Pacific Time'\n  }, {\n    value: 'Europe/London',\n    label: 'London'\n  }, {\n    value: 'Europe/Paris',\n    label: 'Paris'\n  }, {\n    value: 'Asia/Tokyo',\n    label: 'Tokyo'\n  }];\n  const languages = [{\n    value: 'en',\n    label: 'English'\n  }, {\n    value: 'fr',\n    label: 'Français'\n  }, {\n    value: 'es',\n    label: 'Español'\n  }, {\n    value: 'de',\n    label: 'Deutsch'\n  }];\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"Settings - TrustVault\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: \"Manage your account settings and preferences\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        mb: 4,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h1\",\n          gutterBottom: true,\n          children: \"Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          color: \"text.secondary\",\n          children: \"Manage your account preferences and security settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 2,\n                mb: 3,\n                children: [/*#__PURE__*/_jsxDEV(Settings, {\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: \"General\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                flexDirection: \"column\",\n                gap: 3,\n                children: [/*#__PURE__*/_jsxDEV(FormControl, {\n                  fullWidth: true,\n                  children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                    children: \"Timezone\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 270,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    value: settings.timezone,\n                    onChange: e => handleSettingChange('timezone', '', e.target.value),\n                    label: \"Timezone\",\n                    children: timezones.map(tz => /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: tz.value,\n                      children: tz.label\n                    }, tz.value, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 277,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 271,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                  fullWidth: true,\n                  children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                    children: \"Language\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 285,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    value: settings.language,\n                    onChange: e => handleSettingChange('language', '', e.target.value),\n                    label: \"Language\",\n                    children: languages.map(lang => /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: lang.value,\n                      children: lang.label\n                    }, lang.value, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 292,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 286,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                  fullWidth: true,\n                  children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                    children: \"Theme\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 300,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    value: settings.theme,\n                    onChange: e => handleSettingChange('theme', '', e.target.value),\n                    label: \"Theme\",\n                    children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"light\",\n                      children: \"Light\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 306,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"dark\",\n                      children: \"Dark\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 307,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"auto\",\n                      children: \"Auto\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 308,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 2,\n                mb: 3,\n                children: [/*#__PURE__*/_jsxDEV(Notifications, {\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: \"Notifications\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(List, {\n                children: [/*#__PURE__*/_jsxDEV(ListItem, {\n                  children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: \"Email Notifications\",\n                    secondary: \"Receive updates via email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 327,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                    children: /*#__PURE__*/_jsxDEV(Switch, {\n                      checked: settings.notifications.email,\n                      onChange: e => handleSettingChange('notifications', 'email', e.target.checked)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 332,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 331,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                  children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: \"Push Notifications\",\n                    secondary: \"Browser push notifications\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 340,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                    children: /*#__PURE__*/_jsxDEV(Switch, {\n                      checked: settings.notifications.push,\n                      onChange: e => handleSettingChange('notifications', 'push', e.target.checked)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 345,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 344,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                  children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: \"Security Alerts\",\n                    secondary: \"Important security notifications\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 353,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                    children: /*#__PURE__*/_jsxDEV(Switch, {\n                      checked: settings.notifications.security,\n                      onChange: e => handleSettingChange('notifications', 'security', e.target.checked)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 358,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 357,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                  children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: \"Portfolio Updates\",\n                    secondary: \"Portfolio performance notifications\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 366,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                    children: /*#__PURE__*/_jsxDEV(Switch, {\n                      checked: settings.notifications.portfolio,\n                      onChange: e => handleSettingChange('notifications', 'portfolio', e.target.checked)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 371,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 370,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 2,\n                mb: 3,\n                children: [/*#__PURE__*/_jsxDEV(Security, {\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: \"Privacy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                flexDirection: \"column\",\n                gap: 3,\n                children: [/*#__PURE__*/_jsxDEV(FormControl, {\n                  fullWidth: true,\n                  children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                    children: \"Profile Visibility\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 393,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    value: settings.privacy.profile_visibility,\n                    onChange: e => handleSettingChange('privacy', 'profile_visibility', e.target.value),\n                    label: \"Profile Visibility\",\n                    children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"public\",\n                      children: \"Public\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 399,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"private\",\n                      children: \"Private\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 400,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 394,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                  fullWidth: true,\n                  children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                    children: \"Portfolio Visibility\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 405,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    value: settings.privacy.portfolio_visibility,\n                    onChange: e => handleSettingChange('privacy', 'portfolio_visibility', e.target.value),\n                    label: \"Portfolio Visibility\",\n                    children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"public\",\n                      children: \"Public\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 411,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"private\",\n                      children: \"Private\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 412,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 406,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 2,\n                mb: 3,\n                children: [/*#__PURE__*/_jsxDEV(Delete, {\n                  color: \"error\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"error\",\n                  children: \"Danger Zone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Alert, {\n                severity: \"warning\",\n                sx: {\n                  mb: 2\n                },\n                children: \"These actions cannot be undone. Please be careful.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                color: \"error\",\n                startIcon: /*#__PURE__*/_jsxDEV(Delete, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 30\n                }, this),\n                onClick: () => setDeleteAccountDialog(true),\n                fullWidth: true,\n                children: \"Delete Account\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        mt: 4,\n        display: \"flex\",\n        justifyContent: \"flex-end\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(Save, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 24\n          }, this),\n          onClick: handleSaveSettings,\n          disabled: updateSettingsMutation.isLoading,\n          children: updateSettingsMutation.isLoading ? 'Saving...' : 'Save Settings'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 448,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: deleteAccountDialog,\n        onClose: () => setDeleteAccountDialog(false),\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          color: \"error\",\n          children: \"Delete Account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"error\",\n            sx: {\n              mb: 2\n            },\n            children: \"This action will permanently delete your account and all associated data. This cannot be undone.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            gutterBottom: true,\n            children: \"To confirm, please type \\\"DELETE\\\" in the field below:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            value: deleteConfirmation,\n            onChange: e => setDeleteConfirmation(e.target.value),\n            placeholder: \"Type DELETE to confirm\",\n            sx: {\n              mt: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setDeleteAccountDialog(false),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"error\",\n            variant: \"contained\",\n            disabled: deleteConfirmation !== 'DELETE',\n            onClick: () => {\n              // Handle account deletion\n              toast.error('Account deletion not implemented yet');\n              setDeleteAccountDialog(false);\n            },\n            children: \"Delete Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 460,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(SettingsPage, \"mDTOfEKwDewL+aAnkJ7PA/Y8SBw=\", false, function () {\n  return [useAuthStore, useQueryClient, useTheme, useQuery, useQuery, useMutation, useMutation, useMutation, useMutation];\n});\n_c = SettingsPage;\nexport default SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "FormControl", "InputLabel", "Select", "MenuItem", "Switch", "<PERSON><PERSON>", "<PERSON><PERSON>", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "Settings", "Notifications", "Security", "Delete", "Save", "<PERSON><PERSON><PERSON>", "toast", "useMutation", "useQueryClient", "useQuery", "useAuthStore", "useTheme", "apiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SettingsPage", "_s", "user", "queryClient", "themeMode", "setThemeMode", "isDarkMode", "settings", "setSettings", "timezone", "language", "theme", "notifications", "email", "push", "security", "portfolio", "privacy", "profile_visibility", "portfolio_visibility", "deleteAccountDialog", "setDeleteAccountDialog", "deleteConfirmation", "setDeleteConfirmation", "sessionsDialog", "setSessionsDialog", "exportingData", "setExportingData", "data", "userSettings", "isLoading", "settingsLoading", "getUserSettings", "onSuccess", "prev", "onError", "activeSessions", "refetch", "refetchSessions", "getActiveSessions", "enabled", "error", "updateSettingsMutation", "updateUserSettings", "success", "invalidateQueries", "deleteAccountMutation", "confirmation", "deleteAccount", "window", "location", "href", "_error$response", "_error$response$data", "message", "response", "revokeSessionMutation", "sessionId", "revokeSession", "revokeAllSessionsMutation", "revokeAllSessions", "handleSettingChange", "category", "key", "value", "handleThemeChange", "newTheme", "handleSaveSettings", "mutate", "handleExportData", "blob", "exportUserData", "url", "URL", "createObjectURL", "a", "document", "createElement", "download", "Date", "toISOString", "split", "body", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "handleDeleteAccount", "timezones", "label", "languages", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "content", "mb", "variant", "component", "gutterBottom", "color", "container", "spacing", "item", "xs", "md", "display", "alignItems", "gap", "flexDirection", "fullWidth", "onChange", "e", "target", "map", "tz", "lang", "primary", "secondary", "checked", "severity", "sx", "startIcon", "onClick", "mt", "justifyContent", "disabled", "open", "onClose", "max<PERSON><PERSON><PERSON>", "placeholder", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/TrustVault/frontend/src/pages/Settings/SettingsPage.tsx"], "sourcesContent": ["// TrustVault - Settings Page\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Grid,\n  Card,\n  CardContent,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Switch,\n  Button,\n  Alert,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemSecondaryAction,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  CircularProgress,\n  Chip,\n  Divider,\n} from '@mui/material';\nimport {\n  Settings,\n  Notifications,\n  Security,\n  Delete,\n  Save,\n  Download,\n  Devices,\n  Key,\n  Brightness4,\n  Brightness7,\n  BrightnessAuto,\n} from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { toast } from 'react-hot-toast';\nimport { useMutation, useQueryClient, useQuery } from 'react-query';\n\n// Store\nimport { useAuthStore } from '../../store/authStore';\n\n// Contexts\nimport { useTheme } from '../../contexts/ThemeContext';\n\n// Services\nimport apiService from '../../services/api';\n\ninterface UserSettings {\n  timezone: string;\n  language: string;\n  theme: 'light' | 'dark' | 'auto';\n  notifications: {\n    email: boolean;\n    push: boolean;\n    security: boolean;\n    portfolio: boolean;\n  };\n  privacy: {\n    profile_visibility: 'public' | 'private';\n    portfolio_visibility: 'public' | 'private';\n  };\n}\n\nconst SettingsPage: React.FC = () => {\n  const { user } = useAuthStore();\n  const queryClient = useQueryClient();\n  const { themeMode, setThemeMode, isDarkMode } = useTheme();\n\n  const [settings, setSettings] = useState<UserSettings>({\n    timezone: user?.timezone || 'UTC',\n    language: user?.language || 'en',\n    theme: themeMode,\n    notifications: {\n      email: true,\n      push: true,\n      security: true,\n      portfolio: true,\n    },\n    privacy: {\n      profile_visibility: 'private',\n      portfolio_visibility: 'private',\n    },\n  });\n\n  const [deleteAccountDialog, setDeleteAccountDialog] = useState(false);\n  const [deleteConfirmation, setDeleteConfirmation] = useState('');\n  const [sessionsDialog, setSessionsDialog] = useState(false);\n  const [exportingData, setExportingData] = useState(false);\n\n  // Load user settings\n  const { data: userSettings, isLoading: settingsLoading } = useQuery(\n    'user-settings',\n    apiService.getUserSettings,\n    {\n      onSuccess: (data) => {\n        setSettings(prev => ({ ...prev, ...data }));\n      },\n      onError: () => {\n        // Use default settings if API fails\n      },\n    }\n  );\n\n  // Load active sessions\n  const { data: activeSessions, refetch: refetchSessions } = useQuery(\n    'active-sessions',\n    apiService.getActiveSessions,\n    {\n      enabled: sessionsDialog,\n      onError: () => {\n        toast.error('Failed to load active sessions');\n      },\n    }\n  );\n\n  const updateSettingsMutation = useMutation(\n    (data: Partial<UserSettings>) => apiService.updateUserSettings(data),\n    {\n      onSuccess: () => {\n        toast.success('Settings updated successfully');\n        queryClient.invalidateQueries(['user-settings', 'user-profile']);\n      },\n      onError: () => {\n        toast.error('Failed to update settings');\n      },\n    }\n  );\n\n  const deleteAccountMutation = useMutation(\n    (confirmation: string) => apiService.deleteAccount(confirmation),\n    {\n      onSuccess: () => {\n        toast.success('Account deleted successfully');\n        // Redirect to login or home page\n        window.location.href = '/';\n      },\n      onError: (error: any) => {\n        const message = error.response?.data?.message || 'Failed to delete account';\n        toast.error(message);\n      },\n    }\n  );\n\n  const revokeSessionMutation = useMutation(\n    (sessionId: string) => apiService.revokeSession(sessionId),\n    {\n      onSuccess: () => {\n        toast.success('Session revoked successfully');\n        refetchSessions();\n      },\n      onError: () => {\n        toast.error('Failed to revoke session');\n      },\n    }\n  );\n\n  const revokeAllSessionsMutation = useMutation(\n    () => apiService.revokeAllSessions(),\n    {\n      onSuccess: () => {\n        toast.success('All sessions revoked successfully');\n        refetchSessions();\n      },\n      onError: () => {\n        toast.error('Failed to revoke all sessions');\n      },\n    }\n  );\n\n  const handleSettingChange = (category: keyof UserSettings, key: string, value: any) => {\n    setSettings(prev => ({\n      ...prev,\n      [category]: typeof prev[category] === 'object' && prev[category] !== null\n        ? { ...(prev[category] as Record<string, any>), [key]: value }\n        : value\n    }));\n  };\n\n  const handleThemeChange = (newTheme: 'light' | 'dark' | 'auto') => {\n    setThemeMode(newTheme);\n    handleSettingChange('theme', '', newTheme);\n  };\n\n  const handleSaveSettings = () => {\n    updateSettingsMutation.mutate(settings);\n  };\n\n  const handleExportData = async () => {\n    setExportingData(true);\n    try {\n      const blob = await apiService.exportUserData();\n      const url = window.URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = `trustvault-data-${new Date().toISOString().split('T')[0]}.json`;\n      document.body.appendChild(a);\n      a.click();\n      window.URL.revokeObjectURL(url);\n      document.body.removeChild(a);\n      toast.success('Data exported successfully');\n    } catch (error) {\n      toast.error('Failed to export data');\n    } finally {\n      setExportingData(false);\n    }\n  };\n\n  const handleDeleteAccount = () => {\n    if (deleteConfirmation === 'DELETE') {\n      deleteAccountMutation.mutate(deleteConfirmation);\n    }\n  };\n\n  const timezones = [\n    { value: 'UTC', label: 'UTC' },\n    { value: 'America/New_York', label: 'Eastern Time' },\n    { value: 'America/Chicago', label: 'Central Time' },\n    { value: 'America/Denver', label: 'Mountain Time' },\n    { value: 'America/Los_Angeles', label: 'Pacific Time' },\n    { value: 'Europe/London', label: 'London' },\n    { value: 'Europe/Paris', label: 'Paris' },\n    { value: 'Asia/Tokyo', label: 'Tokyo' },\n  ];\n\n  const languages = [\n    { value: 'en', label: 'English' },\n    { value: 'fr', label: 'Français' },\n    { value: 'es', label: 'Español' },\n    { value: 'de', label: 'Deutsch' },\n  ];\n\n  return (\n    <>\n      <Helmet>\n        <title>Settings - TrustVault</title>\n        <meta name=\"description\" content=\"Manage your account settings and preferences\" />\n      </Helmet>\n\n      <Box>\n        {/* Header */}\n        <Box mb={4}>\n          <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n            Settings\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            Manage your account preferences and security settings\n          </Typography>\n        </Box>\n\n        <Grid container spacing={3}>\n          {/* General Settings */}\n          <Grid item xs={12} md={6}>\n            <Card>\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                  <Settings color=\"primary\" />\n                  <Typography variant=\"h6\">General</Typography>\n                </Box>\n\n                <Box display=\"flex\" flexDirection=\"column\" gap={3}>\n                  <FormControl fullWidth>\n                    <InputLabel>Timezone</InputLabel>\n                    <Select\n                      value={settings.timezone}\n                      onChange={(e) => handleSettingChange('timezone', '', e.target.value)}\n                      label=\"Timezone\"\n                    >\n                      {timezones.map((tz) => (\n                        <MenuItem key={tz.value} value={tz.value}>\n                          {tz.label}\n                        </MenuItem>\n                      ))}\n                    </Select>\n                  </FormControl>\n\n                  <FormControl fullWidth>\n                    <InputLabel>Language</InputLabel>\n                    <Select\n                      value={settings.language}\n                      onChange={(e) => handleSettingChange('language', '', e.target.value)}\n                      label=\"Language\"\n                    >\n                      {languages.map((lang) => (\n                        <MenuItem key={lang.value} value={lang.value}>\n                          {lang.label}\n                        </MenuItem>\n                      ))}\n                    </Select>\n                  </FormControl>\n\n                  <FormControl fullWidth>\n                    <InputLabel>Theme</InputLabel>\n                    <Select\n                      value={settings.theme}\n                      onChange={(e) => handleSettingChange('theme', '', e.target.value)}\n                      label=\"Theme\"\n                    >\n                      <MenuItem value=\"light\">Light</MenuItem>\n                      <MenuItem value=\"dark\">Dark</MenuItem>\n                      <MenuItem value=\"auto\">Auto</MenuItem>\n                    </Select>\n                  </FormControl>\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n\n          {/* Notification Settings */}\n          <Grid item xs={12} md={6}>\n            <Card>\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                  <Notifications color=\"primary\" />\n                  <Typography variant=\"h6\">Notifications</Typography>\n                </Box>\n\n                <List>\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Email Notifications\"\n                      secondary=\"Receive updates via email\"\n                    />\n                    <ListItemSecondaryAction>\n                      <Switch\n                        checked={settings.notifications.email}\n                        onChange={(e) => handleSettingChange('notifications', 'email', e.target.checked)}\n                      />\n                    </ListItemSecondaryAction>\n                  </ListItem>\n\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Push Notifications\"\n                      secondary=\"Browser push notifications\"\n                    />\n                    <ListItemSecondaryAction>\n                      <Switch\n                        checked={settings.notifications.push}\n                        onChange={(e) => handleSettingChange('notifications', 'push', e.target.checked)}\n                      />\n                    </ListItemSecondaryAction>\n                  </ListItem>\n\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Security Alerts\"\n                      secondary=\"Important security notifications\"\n                    />\n                    <ListItemSecondaryAction>\n                      <Switch\n                        checked={settings.notifications.security}\n                        onChange={(e) => handleSettingChange('notifications', 'security', e.target.checked)}\n                      />\n                    </ListItemSecondaryAction>\n                  </ListItem>\n\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Portfolio Updates\"\n                      secondary=\"Portfolio performance notifications\"\n                    />\n                    <ListItemSecondaryAction>\n                      <Switch\n                        checked={settings.notifications.portfolio}\n                        onChange={(e) => handleSettingChange('notifications', 'portfolio', e.target.checked)}\n                      />\n                    </ListItemSecondaryAction>\n                  </ListItem>\n                </List>\n              </CardContent>\n            </Card>\n          </Grid>\n\n          {/* Privacy Settings */}\n          <Grid item xs={12} md={6}>\n            <Card>\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                  <Security color=\"primary\" />\n                  <Typography variant=\"h6\">Privacy</Typography>\n                </Box>\n\n                <Box display=\"flex\" flexDirection=\"column\" gap={3}>\n                  <FormControl fullWidth>\n                    <InputLabel>Profile Visibility</InputLabel>\n                    <Select\n                      value={settings.privacy.profile_visibility}\n                      onChange={(e) => handleSettingChange('privacy', 'profile_visibility', e.target.value)}\n                      label=\"Profile Visibility\"\n                    >\n                      <MenuItem value=\"public\">Public</MenuItem>\n                      <MenuItem value=\"private\">Private</MenuItem>\n                    </Select>\n                  </FormControl>\n\n                  <FormControl fullWidth>\n                    <InputLabel>Portfolio Visibility</InputLabel>\n                    <Select\n                      value={settings.privacy.portfolio_visibility}\n                      onChange={(e) => handleSettingChange('privacy', 'portfolio_visibility', e.target.value)}\n                      label=\"Portfolio Visibility\"\n                    >\n                      <MenuItem value=\"public\">Public</MenuItem>\n                      <MenuItem value=\"private\">Private</MenuItem>\n                    </Select>\n                  </FormControl>\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n\n          {/* Danger Zone */}\n          <Grid item xs={12} md={6}>\n            <Card>\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                  <Delete color=\"error\" />\n                  <Typography variant=\"h6\" color=\"error\">Danger Zone</Typography>\n                </Box>\n\n                <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                  These actions cannot be undone. Please be careful.\n                </Alert>\n\n                <Button\n                  variant=\"outlined\"\n                  color=\"error\"\n                  startIcon={<Delete />}\n                  onClick={() => setDeleteAccountDialog(true)}\n                  fullWidth\n                >\n                  Delete Account\n                </Button>\n              </CardContent>\n            </Card>\n          </Grid>\n        </Grid>\n\n        {/* Save Button */}\n        <Box mt={4} display=\"flex\" justifyContent=\"flex-end\">\n          <Button\n            variant=\"contained\"\n            startIcon={<Save />}\n            onClick={handleSaveSettings}\n            disabled={updateSettingsMutation.isLoading}\n          >\n            {updateSettingsMutation.isLoading ? 'Saving...' : 'Save Settings'}\n          </Button>\n        </Box>\n\n        {/* Delete Account Dialog */}\n        <Dialog\n          open={deleteAccountDialog}\n          onClose={() => setDeleteAccountDialog(false)}\n          maxWidth=\"sm\"\n          fullWidth\n        >\n          <DialogTitle color=\"error\">Delete Account</DialogTitle>\n          <DialogContent>\n            <Alert severity=\"error\" sx={{ mb: 2 }}>\n              This action will permanently delete your account and all associated data.\n              This cannot be undone.\n            </Alert>\n            \n            <Typography variant=\"body2\" gutterBottom>\n              To confirm, please type \"DELETE\" in the field below:\n            </Typography>\n            \n            <TextField\n              fullWidth\n              value={deleteConfirmation}\n              onChange={(e) => setDeleteConfirmation(e.target.value)}\n              placeholder=\"Type DELETE to confirm\"\n              sx={{ mt: 2 }}\n            />\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={() => setDeleteAccountDialog(false)}>\n              Cancel\n            </Button>\n            <Button\n              color=\"error\"\n              variant=\"contained\"\n              disabled={deleteConfirmation !== 'DELETE'}\n              onClick={() => {\n                // Handle account deletion\n                toast.error('Account deletion not implemented yet');\n                setDeleteAccountDialog(false);\n              }}\n            >\n              Delete Account\n            </Button>\n          </DialogActions>\n        </Dialog>\n      </Box>\n    </>\n  );\n};\n\nexport default SettingsPage;\n"], "mappings": ";;AAAA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,QAAmB,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,QAIJ,eAAe;AACtB,SACEC,QAAQ,EACRC,aAAa,EACbC,QAAQ,EACRC,MAAM,EACNC,IAAI,QAOC,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,KAAK,QAAQ,iBAAiB;AACvC,SAASC,WAAW,EAAEC,cAAc,EAAEC,QAAQ,QAAQ,aAAa;;AAEnE;AACA,SAASC,YAAY,QAAQ,uBAAuB;;AAEpD;AACA,SAASC,QAAQ,QAAQ,6BAA6B;;AAEtD;AACA,OAAOC,UAAU,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAkB5C,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM;IAAEC;EAAK,CAAC,GAAGT,YAAY,CAAC,CAAC;EAC/B,MAAMU,WAAW,GAAGZ,cAAc,CAAC,CAAC;EACpC,MAAM;IAAEa,SAAS;IAAEC,YAAY;IAAEC;EAAW,CAAC,GAAGZ,QAAQ,CAAC,CAAC;EAE1D,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAG/C,QAAQ,CAAe;IACrDgD,QAAQ,EAAE,CAAAP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,QAAQ,KAAI,KAAK;IACjCC,QAAQ,EAAE,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,QAAQ,KAAI,IAAI;IAChCC,KAAK,EAAEP,SAAS;IAChBQ,aAAa,EAAE;MACbC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAE;IACb,CAAC;IACDC,OAAO,EAAE;MACPC,kBAAkB,EAAE,SAAS;MAC7BC,oBAAoB,EAAE;IACxB;EACF,CAAC,CAAC;EAEF,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC6D,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAAC+D,cAAc,EAAEC,iBAAiB,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACiE,aAAa,EAAEC,gBAAgB,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM;IAAEmE,IAAI,EAAEC,YAAY;IAAEC,SAAS,EAAEC;EAAgB,CAAC,GAAGvC,QAAQ,CACjE,eAAe,EACfG,UAAU,CAACqC,eAAe,EAC1B;IACEC,SAAS,EAAGL,IAAI,IAAK;MACnBpB,WAAW,CAAC0B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,GAAGN;MAAK,CAAC,CAAC,CAAC;IAC7C,CAAC;IACDO,OAAO,EAAEA,CAAA,KAAM;MACb;IAAA;EAEJ,CACF,CAAC;;EAED;EACA,MAAM;IAAEP,IAAI,EAAEQ,cAAc;IAAEC,OAAO,EAAEC;EAAgB,CAAC,GAAG9C,QAAQ,CACjE,iBAAiB,EACjBG,UAAU,CAAC4C,iBAAiB,EAC5B;IACEC,OAAO,EAAEhB,cAAc;IACvBW,OAAO,EAAEA,CAAA,KAAM;MACb9C,KAAK,CAACoD,KAAK,CAAC,gCAAgC,CAAC;IAC/C;EACF,CACF,CAAC;EAED,MAAMC,sBAAsB,GAAGpD,WAAW,CACvCsC,IAA2B,IAAKjC,UAAU,CAACgD,kBAAkB,CAACf,IAAI,CAAC,EACpE;IACEK,SAAS,EAAEA,CAAA,KAAM;MACf5C,KAAK,CAACuD,OAAO,CAAC,+BAA+B,CAAC;MAC9CzC,WAAW,CAAC0C,iBAAiB,CAAC,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC;IAClE,CAAC;IACDV,OAAO,EAAEA,CAAA,KAAM;MACb9C,KAAK,CAACoD,KAAK,CAAC,2BAA2B,CAAC;IAC1C;EACF,CACF,CAAC;EAED,MAAMK,qBAAqB,GAAGxD,WAAW,CACtCyD,YAAoB,IAAKpD,UAAU,CAACqD,aAAa,CAACD,YAAY,CAAC,EAChE;IACEd,SAAS,EAAEA,CAAA,KAAM;MACf5C,KAAK,CAACuD,OAAO,CAAC,8BAA8B,CAAC;MAC7C;MACAK,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;IAC5B,CAAC;IACDhB,OAAO,EAAGM,KAAU,IAAK;MAAA,IAAAW,eAAA,EAAAC,oBAAA;MACvB,MAAMC,OAAO,GAAG,EAAAF,eAAA,GAAAX,KAAK,CAACc,QAAQ,cAAAH,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBxB,IAAI,cAAAyB,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,0BAA0B;MAC3EjE,KAAK,CAACoD,KAAK,CAACa,OAAO,CAAC;IACtB;EACF,CACF,CAAC;EAED,MAAME,qBAAqB,GAAGlE,WAAW,CACtCmE,SAAiB,IAAK9D,UAAU,CAAC+D,aAAa,CAACD,SAAS,CAAC,EAC1D;IACExB,SAAS,EAAEA,CAAA,KAAM;MACf5C,KAAK,CAACuD,OAAO,CAAC,8BAA8B,CAAC;MAC7CN,eAAe,CAAC,CAAC;IACnB,CAAC;IACDH,OAAO,EAAEA,CAAA,KAAM;MACb9C,KAAK,CAACoD,KAAK,CAAC,0BAA0B,CAAC;IACzC;EACF,CACF,CAAC;EAED,MAAMkB,yBAAyB,GAAGrE,WAAW,CAC3C,MAAMK,UAAU,CAACiE,iBAAiB,CAAC,CAAC,EACpC;IACE3B,SAAS,EAAEA,CAAA,KAAM;MACf5C,KAAK,CAACuD,OAAO,CAAC,mCAAmC,CAAC;MAClDN,eAAe,CAAC,CAAC;IACnB,CAAC;IACDH,OAAO,EAAEA,CAAA,KAAM;MACb9C,KAAK,CAACoD,KAAK,CAAC,+BAA+B,CAAC;IAC9C;EACF,CACF,CAAC;EAED,MAAMoB,mBAAmB,GAAGA,CAACC,QAA4B,EAAEC,GAAW,EAAEC,KAAU,KAAK;IACrFxD,WAAW,CAAC0B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAAC4B,QAAQ,GAAG,OAAO5B,IAAI,CAAC4B,QAAQ,CAAC,KAAK,QAAQ,IAAI5B,IAAI,CAAC4B,QAAQ,CAAC,KAAK,IAAI,GACrE;QAAE,GAAI5B,IAAI,CAAC4B,QAAQ,CAAyB;QAAE,CAACC,GAAG,GAAGC;MAAM,CAAC,GAC5DA;IACN,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,iBAAiB,GAAIC,QAAmC,IAAK;IACjE7D,YAAY,CAAC6D,QAAQ,CAAC;IACtBL,mBAAmB,CAAC,OAAO,EAAE,EAAE,EAAEK,QAAQ,CAAC;EAC5C,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BzB,sBAAsB,CAAC0B,MAAM,CAAC7D,QAAQ,CAAC;EACzC,CAAC;EAED,MAAM8D,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC1C,gBAAgB,CAAC,IAAI,CAAC;IACtB,IAAI;MACF,MAAM2C,IAAI,GAAG,MAAM3E,UAAU,CAAC4E,cAAc,CAAC,CAAC;MAC9C,MAAMC,GAAG,GAAGvB,MAAM,CAACwB,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;MAC5C,MAAMK,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACrCF,CAAC,CAACxB,IAAI,GAAGqB,GAAG;MACZG,CAAC,CAACG,QAAQ,GAAG,mBAAmB,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO;MAC7EL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,CAAC,CAAC;MAC5BA,CAAC,CAACS,KAAK,CAAC,CAAC;MACTnC,MAAM,CAACwB,GAAG,CAACY,eAAe,CAACb,GAAG,CAAC;MAC/BI,QAAQ,CAACM,IAAI,CAACI,WAAW,CAACX,CAAC,CAAC;MAC5BtF,KAAK,CAACuD,OAAO,CAAC,4BAA4B,CAAC;IAC7C,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdpD,KAAK,CAACoD,KAAK,CAAC,uBAAuB,CAAC;IACtC,CAAC,SAAS;MACRd,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAM4D,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIjE,kBAAkB,KAAK,QAAQ,EAAE;MACnCwB,qBAAqB,CAACsB,MAAM,CAAC9C,kBAAkB,CAAC;IAClD;EACF,CAAC;EAED,MAAMkE,SAAS,GAAG,CAChB;IAAExB,KAAK,EAAE,KAAK;IAAEyB,KAAK,EAAE;EAAM,CAAC,EAC9B;IAAEzB,KAAK,EAAE,kBAAkB;IAAEyB,KAAK,EAAE;EAAe,CAAC,EACpD;IAAEzB,KAAK,EAAE,iBAAiB;IAAEyB,KAAK,EAAE;EAAe,CAAC,EACnD;IAAEzB,KAAK,EAAE,gBAAgB;IAAEyB,KAAK,EAAE;EAAgB,CAAC,EACnD;IAAEzB,KAAK,EAAE,qBAAqB;IAAEyB,KAAK,EAAE;EAAe,CAAC,EACvD;IAAEzB,KAAK,EAAE,eAAe;IAAEyB,KAAK,EAAE;EAAS,CAAC,EAC3C;IAAEzB,KAAK,EAAE,cAAc;IAAEyB,KAAK,EAAE;EAAQ,CAAC,EACzC;IAAEzB,KAAK,EAAE,YAAY;IAAEyB,KAAK,EAAE;EAAQ,CAAC,CACxC;EAED,MAAMC,SAAS,GAAG,CAChB;IAAE1B,KAAK,EAAE,IAAI;IAAEyB,KAAK,EAAE;EAAU,CAAC,EACjC;IAAEzB,KAAK,EAAE,IAAI;IAAEyB,KAAK,EAAE;EAAW,CAAC,EAClC;IAAEzB,KAAK,EAAE,IAAI;IAAEyB,KAAK,EAAE;EAAU,CAAC,EACjC;IAAEzB,KAAK,EAAE,IAAI;IAAEyB,KAAK,EAAE;EAAU,CAAC,CAClC;EAED,oBACE5F,OAAA,CAAAE,SAAA;IAAA4F,QAAA,gBACE9F,OAAA,CAACT,MAAM;MAAAuG,QAAA,gBACL9F,OAAA;QAAA8F,QAAA,EAAO;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACpClG,OAAA;QAAMmG,IAAI,EAAC,aAAa;QAACC,OAAO,EAAC;MAA8C;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5E,CAAC,eAETlG,OAAA,CAACnC,GAAG;MAAAiI,QAAA,gBAEF9F,OAAA,CAACnC,GAAG;QAACwI,EAAE,EAAE,CAAE;QAAAP,QAAA,gBACT9F,OAAA,CAAClC,UAAU;UAACwI,OAAO,EAAC,IAAI;UAACC,SAAS,EAAC,IAAI;UAACC,YAAY;UAAAV,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACblG,OAAA,CAAClC,UAAU;UAACwI,OAAO,EAAC,OAAO;UAACG,KAAK,EAAC,gBAAgB;UAAAX,QAAA,EAAC;QAEnD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAENlG,OAAA,CAACjC,IAAI;QAAC2I,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAb,QAAA,gBAEzB9F,OAAA,CAACjC,IAAI;UAAC6I,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eACvB9F,OAAA,CAAChC,IAAI;YAAA8H,QAAA,eACH9F,OAAA,CAAC/B,WAAW;cAAA6H,QAAA,gBACV9F,OAAA,CAACnC,GAAG;gBAACkJ,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,GAAG,EAAE,CAAE;gBAACZ,EAAE,EAAE,CAAE;gBAAAP,QAAA,gBACpD9F,OAAA,CAACd,QAAQ;kBAACuH,KAAK,EAAC;gBAAS;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5BlG,OAAA,CAAClC,UAAU;kBAACwI,OAAO,EAAC,IAAI;kBAAAR,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eAENlG,OAAA,CAACnC,GAAG;gBAACkJ,OAAO,EAAC,MAAM;gBAACG,aAAa,EAAC,QAAQ;gBAACD,GAAG,EAAE,CAAE;gBAAAnB,QAAA,gBAChD9F,OAAA,CAAC9B,WAAW;kBAACiJ,SAAS;kBAAArB,QAAA,gBACpB9F,OAAA,CAAC7B,UAAU;oBAAA2H,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACjClG,OAAA,CAAC5B,MAAM;oBACL+F,KAAK,EAAEzD,QAAQ,CAACE,QAAS;oBACzBwG,QAAQ,EAAGC,CAAC,IAAKrD,mBAAmB,CAAC,UAAU,EAAE,EAAE,EAAEqD,CAAC,CAACC,MAAM,CAACnD,KAAK,CAAE;oBACrEyB,KAAK,EAAC,UAAU;oBAAAE,QAAA,EAEfH,SAAS,CAAC4B,GAAG,CAAEC,EAAE,iBAChBxH,OAAA,CAAC3B,QAAQ;sBAAgB8F,KAAK,EAAEqD,EAAE,CAACrD,KAAM;sBAAA2B,QAAA,EACtC0B,EAAE,CAAC5B;oBAAK,GADI4B,EAAE,CAACrD,KAAK;sBAAA4B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEb,CACX;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAEdlG,OAAA,CAAC9B,WAAW;kBAACiJ,SAAS;kBAAArB,QAAA,gBACpB9F,OAAA,CAAC7B,UAAU;oBAAA2H,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACjClG,OAAA,CAAC5B,MAAM;oBACL+F,KAAK,EAAEzD,QAAQ,CAACG,QAAS;oBACzBuG,QAAQ,EAAGC,CAAC,IAAKrD,mBAAmB,CAAC,UAAU,EAAE,EAAE,EAAEqD,CAAC,CAACC,MAAM,CAACnD,KAAK,CAAE;oBACrEyB,KAAK,EAAC,UAAU;oBAAAE,QAAA,EAEfD,SAAS,CAAC0B,GAAG,CAAEE,IAAI,iBAClBzH,OAAA,CAAC3B,QAAQ;sBAAkB8F,KAAK,EAAEsD,IAAI,CAACtD,KAAM;sBAAA2B,QAAA,EAC1C2B,IAAI,CAAC7B;oBAAK,GADE6B,IAAI,CAACtD,KAAK;sBAAA4B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEf,CACX;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAEdlG,OAAA,CAAC9B,WAAW;kBAACiJ,SAAS;kBAAArB,QAAA,gBACpB9F,OAAA,CAAC7B,UAAU;oBAAA2H,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC9BlG,OAAA,CAAC5B,MAAM;oBACL+F,KAAK,EAAEzD,QAAQ,CAACI,KAAM;oBACtBsG,QAAQ,EAAGC,CAAC,IAAKrD,mBAAmB,CAAC,OAAO,EAAE,EAAE,EAAEqD,CAAC,CAACC,MAAM,CAACnD,KAAK,CAAE;oBAClEyB,KAAK,EAAC,OAAO;oBAAAE,QAAA,gBAEb9F,OAAA,CAAC3B,QAAQ;sBAAC8F,KAAK,EAAC,OAAO;sBAAA2B,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eACxClG,OAAA,CAAC3B,QAAQ;sBAAC8F,KAAK,EAAC,MAAM;sBAAA2B,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eACtClG,OAAA,CAAC3B,QAAQ;sBAAC8F,KAAK,EAAC,MAAM;sBAAA2B,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGPlG,OAAA,CAACjC,IAAI;UAAC6I,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eACvB9F,OAAA,CAAChC,IAAI;YAAA8H,QAAA,eACH9F,OAAA,CAAC/B,WAAW;cAAA6H,QAAA,gBACV9F,OAAA,CAACnC,GAAG;gBAACkJ,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,GAAG,EAAE,CAAE;gBAACZ,EAAE,EAAE,CAAE;gBAAAP,QAAA,gBACpD9F,OAAA,CAACb,aAAa;kBAACsH,KAAK,EAAC;gBAAS;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjClG,OAAA,CAAClC,UAAU;kBAACwI,OAAO,EAAC,IAAI;kBAAAR,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eAENlG,OAAA,CAACvB,IAAI;gBAAAqH,QAAA,gBACH9F,OAAA,CAACtB,QAAQ;kBAAAoH,QAAA,gBACP9F,OAAA,CAACrB,YAAY;oBACX+I,OAAO,EAAC,qBAAqB;oBAC7BC,SAAS,EAAC;kBAA2B;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,eACFlG,OAAA,CAACpB,uBAAuB;oBAAAkH,QAAA,eACtB9F,OAAA,CAAC1B,MAAM;sBACLsJ,OAAO,EAAElH,QAAQ,CAACK,aAAa,CAACC,KAAM;sBACtCoG,QAAQ,EAAGC,CAAC,IAAKrD,mBAAmB,CAAC,eAAe,EAAE,OAAO,EAAEqD,CAAC,CAACC,MAAM,CAACM,OAAO;oBAAE;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACqB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eAEXlG,OAAA,CAACtB,QAAQ;kBAAAoH,QAAA,gBACP9F,OAAA,CAACrB,YAAY;oBACX+I,OAAO,EAAC,oBAAoB;oBAC5BC,SAAS,EAAC;kBAA4B;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC,eACFlG,OAAA,CAACpB,uBAAuB;oBAAAkH,QAAA,eACtB9F,OAAA,CAAC1B,MAAM;sBACLsJ,OAAO,EAAElH,QAAQ,CAACK,aAAa,CAACE,IAAK;sBACrCmG,QAAQ,EAAGC,CAAC,IAAKrD,mBAAmB,CAAC,eAAe,EAAE,MAAM,EAAEqD,CAAC,CAACC,MAAM,CAACM,OAAO;oBAAE;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACqB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eAEXlG,OAAA,CAACtB,QAAQ;kBAAAoH,QAAA,gBACP9F,OAAA,CAACrB,YAAY;oBACX+I,OAAO,EAAC,iBAAiB;oBACzBC,SAAS,EAAC;kBAAkC;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC,eACFlG,OAAA,CAACpB,uBAAuB;oBAAAkH,QAAA,eACtB9F,OAAA,CAAC1B,MAAM;sBACLsJ,OAAO,EAAElH,QAAQ,CAACK,aAAa,CAACG,QAAS;sBACzCkG,QAAQ,EAAGC,CAAC,IAAKrD,mBAAmB,CAAC,eAAe,EAAE,UAAU,EAAEqD,CAAC,CAACC,MAAM,CAACM,OAAO;oBAAE;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACqB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eAEXlG,OAAA,CAACtB,QAAQ;kBAAAoH,QAAA,gBACP9F,OAAA,CAACrB,YAAY;oBACX+I,OAAO,EAAC,mBAAmB;oBAC3BC,SAAS,EAAC;kBAAqC;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC,eACFlG,OAAA,CAACpB,uBAAuB;oBAAAkH,QAAA,eACtB9F,OAAA,CAAC1B,MAAM;sBACLsJ,OAAO,EAAElH,QAAQ,CAACK,aAAa,CAACI,SAAU;sBAC1CiG,QAAQ,EAAGC,CAAC,IAAKrD,mBAAmB,CAAC,eAAe,EAAE,WAAW,EAAEqD,CAAC,CAACC,MAAM,CAACM,OAAO;oBAAE;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACqB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGPlG,OAAA,CAACjC,IAAI;UAAC6I,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eACvB9F,OAAA,CAAChC,IAAI;YAAA8H,QAAA,eACH9F,OAAA,CAAC/B,WAAW;cAAA6H,QAAA,gBACV9F,OAAA,CAACnC,GAAG;gBAACkJ,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,GAAG,EAAE,CAAE;gBAACZ,EAAE,EAAE,CAAE;gBAAAP,QAAA,gBACpD9F,OAAA,CAACZ,QAAQ;kBAACqH,KAAK,EAAC;gBAAS;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5BlG,OAAA,CAAClC,UAAU;kBAACwI,OAAO,EAAC,IAAI;kBAAAR,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eAENlG,OAAA,CAACnC,GAAG;gBAACkJ,OAAO,EAAC,MAAM;gBAACG,aAAa,EAAC,QAAQ;gBAACD,GAAG,EAAE,CAAE;gBAAAnB,QAAA,gBAChD9F,OAAA,CAAC9B,WAAW;kBAACiJ,SAAS;kBAAArB,QAAA,gBACpB9F,OAAA,CAAC7B,UAAU;oBAAA2H,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC3ClG,OAAA,CAAC5B,MAAM;oBACL+F,KAAK,EAAEzD,QAAQ,CAACU,OAAO,CAACC,kBAAmB;oBAC3C+F,QAAQ,EAAGC,CAAC,IAAKrD,mBAAmB,CAAC,SAAS,EAAE,oBAAoB,EAAEqD,CAAC,CAACC,MAAM,CAACnD,KAAK,CAAE;oBACtFyB,KAAK,EAAC,oBAAoB;oBAAAE,QAAA,gBAE1B9F,OAAA,CAAC3B,QAAQ;sBAAC8F,KAAK,EAAC,QAAQ;sBAAA2B,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eAC1ClG,OAAA,CAAC3B,QAAQ;sBAAC8F,KAAK,EAAC,SAAS;sBAAA2B,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAEdlG,OAAA,CAAC9B,WAAW;kBAACiJ,SAAS;kBAAArB,QAAA,gBACpB9F,OAAA,CAAC7B,UAAU;oBAAA2H,QAAA,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC7ClG,OAAA,CAAC5B,MAAM;oBACL+F,KAAK,EAAEzD,QAAQ,CAACU,OAAO,CAACE,oBAAqB;oBAC7C8F,QAAQ,EAAGC,CAAC,IAAKrD,mBAAmB,CAAC,SAAS,EAAE,sBAAsB,EAAEqD,CAAC,CAACC,MAAM,CAACnD,KAAK,CAAE;oBACxFyB,KAAK,EAAC,sBAAsB;oBAAAE,QAAA,gBAE5B9F,OAAA,CAAC3B,QAAQ;sBAAC8F,KAAK,EAAC,QAAQ;sBAAA2B,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eAC1ClG,OAAA,CAAC3B,QAAQ;sBAAC8F,KAAK,EAAC,SAAS;sBAAA2B,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGPlG,OAAA,CAACjC,IAAI;UAAC6I,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eACvB9F,OAAA,CAAChC,IAAI;YAAA8H,QAAA,eACH9F,OAAA,CAAC/B,WAAW;cAAA6H,QAAA,gBACV9F,OAAA,CAACnC,GAAG;gBAACkJ,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,GAAG,EAAE,CAAE;gBAACZ,EAAE,EAAE,CAAE;gBAAAP,QAAA,gBACpD9F,OAAA,CAACX,MAAM;kBAACoH,KAAK,EAAC;gBAAO;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxBlG,OAAA,CAAClC,UAAU;kBAACwI,OAAO,EAAC,IAAI;kBAACG,KAAK,EAAC,OAAO;kBAAAX,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,eAENlG,OAAA,CAACxB,KAAK;gBAACqJ,QAAQ,EAAC,SAAS;gBAACC,EAAE,EAAE;kBAAEzB,EAAE,EAAE;gBAAE,CAAE;gBAAAP,QAAA,EAAC;cAEzC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAERlG,OAAA,CAACzB,MAAM;gBACL+H,OAAO,EAAC,UAAU;gBAClBG,KAAK,EAAC,OAAO;gBACbsB,SAAS,eAAE/H,OAAA,CAACX,MAAM;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACtB8B,OAAO,EAAEA,CAAA,KAAMxG,sBAAsB,CAAC,IAAI,CAAE;gBAC5C2F,SAAS;gBAAArB,QAAA,EACV;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPlG,OAAA,CAACnC,GAAG;QAACoK,EAAE,EAAE,CAAE;QAAClB,OAAO,EAAC,MAAM;QAACmB,cAAc,EAAC,UAAU;QAAApC,QAAA,eAClD9F,OAAA,CAACzB,MAAM;UACL+H,OAAO,EAAC,WAAW;UACnByB,SAAS,eAAE/H,OAAA,CAACV,IAAI;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACpB8B,OAAO,EAAE1D,kBAAmB;UAC5B6D,QAAQ,EAAEtF,sBAAsB,CAACZ,SAAU;UAAA6D,QAAA,EAE1CjD,sBAAsB,CAACZ,SAAS,GAAG,WAAW,GAAG;QAAe;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNlG,OAAA,CAACnB,MAAM;QACLuJ,IAAI,EAAE7G,mBAAoB;QAC1B8G,OAAO,EAAEA,CAAA,KAAM7G,sBAAsB,CAAC,KAAK,CAAE;QAC7C8G,QAAQ,EAAC,IAAI;QACbnB,SAAS;QAAArB,QAAA,gBAET9F,OAAA,CAAClB,WAAW;UAAC2H,KAAK,EAAC,OAAO;UAAAX,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACvDlG,OAAA,CAACjB,aAAa;UAAA+G,QAAA,gBACZ9F,OAAA,CAACxB,KAAK;YAACqJ,QAAQ,EAAC,OAAO;YAACC,EAAE,EAAE;cAAEzB,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,EAAC;UAGvC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAERlG,OAAA,CAAClC,UAAU;YAACwI,OAAO,EAAC,OAAO;YAACE,YAAY;YAAAV,QAAA,EAAC;UAEzC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEblG,OAAA,CAACf,SAAS;YACRkI,SAAS;YACThD,KAAK,EAAE1C,kBAAmB;YAC1B2F,QAAQ,EAAGC,CAAC,IAAK3F,qBAAqB,CAAC2F,CAAC,CAACC,MAAM,CAACnD,KAAK,CAAE;YACvDoE,WAAW,EAAC,wBAAwB;YACpCT,EAAE,EAAE;cAAEG,EAAE,EAAE;YAAE;UAAE;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC,eAChBlG,OAAA,CAAChB,aAAa;UAAA8G,QAAA,gBACZ9F,OAAA,CAACzB,MAAM;YAACyJ,OAAO,EAAEA,CAAA,KAAMxG,sBAAsB,CAAC,KAAK,CAAE;YAAAsE,QAAA,EAAC;UAEtD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTlG,OAAA,CAACzB,MAAM;YACLkI,KAAK,EAAC,OAAO;YACbH,OAAO,EAAC,WAAW;YACnB6B,QAAQ,EAAE1G,kBAAkB,KAAK,QAAS;YAC1CuG,OAAO,EAAEA,CAAA,KAAM;cACb;cACAxI,KAAK,CAACoD,KAAK,CAAC,sCAAsC,CAAC;cACnDpB,sBAAsB,CAAC,KAAK,CAAC;YAC/B,CAAE;YAAAsE,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAAC9F,EAAA,CAlbID,YAAsB;EAAA,QACTP,YAAY,EACTF,cAAc,EACcG,QAAQ,EAwBGF,QAAQ,EAcRA,QAAQ,EAWpCF,WAAW,EAaZA,WAAW,EAeXA,WAAW,EAaPA,WAAW;AAAA;AAAA+I,EAAA,GA7FzCrI,YAAsB;AAob5B,eAAeA,YAAY;AAAC,IAAAqI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}