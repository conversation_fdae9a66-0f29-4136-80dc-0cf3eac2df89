{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\TrustVault\\\\frontend\\\\src\\\\contexts\\\\ThemeContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\n// TrustVault - Theme Context\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport { CssBaseline } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ThemeContext = /*#__PURE__*/createContext(undefined);\nexport const useTheme = () => {\n  _s();\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n_s(useTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const CustomThemeProvider = ({\n  children\n}) => {\n  _s2();\n  const [themeMode, setThemeModeState] = useState(() => {\n    const saved = localStorage.getItem('theme-mode');\n    return saved || 'light';\n  });\n  const [isDarkMode, setIsDarkMode] = useState(false);\n\n  // Detect system theme preference\n  const getSystemTheme = () => {\n    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n  };\n\n  // Update theme based on mode\n  useEffect(() => {\n    let actualTheme;\n    if (themeMode === 'auto') {\n      actualTheme = getSystemTheme();\n    } else {\n      actualTheme = themeMode;\n    }\n    setIsDarkMode(actualTheme === 'dark');\n  }, [themeMode]);\n\n  // Listen for system theme changes when in auto mode\n  useEffect(() => {\n    if (themeMode === 'auto') {\n      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n      const handleChange = () => {\n        setIsDarkMode(mediaQuery.matches);\n      };\n      mediaQuery.addEventListener('change', handleChange);\n      return () => mediaQuery.removeEventListener('change', handleChange);\n    }\n  }, [themeMode]);\n  const setThemeMode = mode => {\n    setThemeModeState(mode);\n    localStorage.setItem('theme-mode', mode);\n  };\n\n  // Create Material-UI theme\n  const theme = createTheme({\n    palette: {\n      mode: isDarkMode ? 'dark' : 'light',\n      primary: {\n        main: '#1976d2',\n        light: '#42a5f5',\n        dark: '#1565c0'\n      },\n      secondary: {\n        main: '#dc004e',\n        light: '#ff5983',\n        dark: '#9a0036'\n      },\n      background: {\n        default: isDarkMode ? '#121212' : '#fafafa',\n        paper: isDarkMode ? '#1e1e1e' : '#ffffff'\n      },\n      text: {\n        primary: isDarkMode ? '#ffffff' : '#000000',\n        secondary: isDarkMode ? '#b3b3b3' : '#666666'\n      }\n    },\n    typography: {\n      fontFamily: '\"Inter\", \"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n      h1: {\n        fontWeight: 600\n      },\n      h2: {\n        fontWeight: 600\n      },\n      h3: {\n        fontWeight: 600\n      },\n      h4: {\n        fontWeight: 600\n      },\n      h5: {\n        fontWeight: 600\n      },\n      h6: {\n        fontWeight: 600\n      }\n    },\n    shape: {\n      borderRadius: 8\n    },\n    components: {\n      MuiCard: {\n        styleOverrides: {\n          root: {\n            boxShadow: isDarkMode ? '0 2px 8px rgba(0,0,0,0.3)' : '0 2px 8px rgba(0,0,0,0.1)'\n          }\n        }\n      },\n      MuiButton: {\n        styleOverrides: {\n          root: {\n            textTransform: 'none',\n            fontWeight: 500\n          }\n        }\n      },\n      MuiAppBar: {\n        styleOverrides: {\n          root: {\n            backgroundColor: isDarkMode ? '#1e1e1e' : '#ffffff',\n            color: isDarkMode ? '#ffffff' : '#000000'\n          }\n        }\n      }\n    }\n  });\n  const contextValue = {\n    themeMode,\n    setThemeMode,\n    isDarkMode\n  };\n  return /*#__PURE__*/_jsxDEV(ThemeContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsxDEV(ThemeProvider, {\n      theme: theme,\n      children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), children]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 156,\n    columnNumber: 5\n  }, this);\n};\n_s2(CustomThemeProvider, \"oO6mFMcxQFt4nGR4DBXCLcZYPCM=\");\n_c = CustomThemeProvider;\nvar _c;\n$RefreshReg$(_c, \"CustomThemeProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "ThemeProvider", "createTheme", "CssBaseline", "jsxDEV", "_jsxDEV", "ThemeContext", "undefined", "useTheme", "_s", "context", "Error", "CustomThemeProvider", "children", "_s2", "themeMode", "setThemeModeState", "saved", "localStorage", "getItem", "isDarkMode", "setIsDarkMode", "getSystemTheme", "window", "matchMedia", "matches", "actualTheme", "mediaQuery", "handleChange", "addEventListener", "removeEventListener", "setThemeMode", "mode", "setItem", "theme", "palette", "primary", "main", "light", "dark", "secondary", "background", "default", "paper", "text", "typography", "fontFamily", "h1", "fontWeight", "h2", "h3", "h4", "h5", "h6", "shape", "borderRadius", "components", "MuiCard", "styleOverrides", "root", "boxShadow", "MuiB<PERSON>on", "textTransform", "MuiAppBar", "backgroundColor", "color", "contextValue", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/TrustVault/frontend/src/contexts/ThemeContext.tsx"], "sourcesContent": ["// TrustVault - Theme Context\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport { CssBaseline } from '@mui/material';\n\ntype ThemeMode = 'light' | 'dark' | 'auto';\n\ninterface ThemeContextType {\n  themeMode: ThemeMode;\n  setThemeMode: (mode: ThemeMode) => void;\n  isDarkMode: boolean;\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport const useTheme = () => {\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n\ninterface CustomThemeProviderProps {\n  children: ReactNode;\n}\n\nexport const CustomThemeProvider: React.FC<CustomThemeProviderProps> = ({ children }) => {\n  const [themeMode, setThemeModeState] = useState<ThemeMode>(() => {\n    const saved = localStorage.getItem('theme-mode');\n    return (saved as ThemeMode) || 'light';\n  });\n\n  const [isDarkMode, setIsDarkMode] = useState(false);\n\n  // Detect system theme preference\n  const getSystemTheme = () => {\n    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n  };\n\n  // Update theme based on mode\n  useEffect(() => {\n    let actualTheme: 'light' | 'dark';\n    \n    if (themeMode === 'auto') {\n      actualTheme = getSystemTheme();\n    } else {\n      actualTheme = themeMode;\n    }\n    \n    setIsDarkMode(actualTheme === 'dark');\n  }, [themeMode]);\n\n  // Listen for system theme changes when in auto mode\n  useEffect(() => {\n    if (themeMode === 'auto') {\n      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n      const handleChange = () => {\n        setIsDarkMode(mediaQuery.matches);\n      };\n      \n      mediaQuery.addEventListener('change', handleChange);\n      return () => mediaQuery.removeEventListener('change', handleChange);\n    }\n  }, [themeMode]);\n\n  const setThemeMode = (mode: ThemeMode) => {\n    setThemeModeState(mode);\n    localStorage.setItem('theme-mode', mode);\n  };\n\n  // Create Material-UI theme\n  const theme = createTheme({\n    palette: {\n      mode: isDarkMode ? 'dark' : 'light',\n      primary: {\n        main: '#1976d2',\n        light: '#42a5f5',\n        dark: '#1565c0',\n      },\n      secondary: {\n        main: '#dc004e',\n        light: '#ff5983',\n        dark: '#9a0036',\n      },\n      background: {\n        default: isDarkMode ? '#121212' : '#fafafa',\n        paper: isDarkMode ? '#1e1e1e' : '#ffffff',\n      },\n      text: {\n        primary: isDarkMode ? '#ffffff' : '#000000',\n        secondary: isDarkMode ? '#b3b3b3' : '#666666',\n      },\n    },\n    typography: {\n      fontFamily: '\"Inter\", \"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n      h1: {\n        fontWeight: 600,\n      },\n      h2: {\n        fontWeight: 600,\n      },\n      h3: {\n        fontWeight: 600,\n      },\n      h4: {\n        fontWeight: 600,\n      },\n      h5: {\n        fontWeight: 600,\n      },\n      h6: {\n        fontWeight: 600,\n      },\n    },\n    shape: {\n      borderRadius: 8,\n    },\n    components: {\n      MuiCard: {\n        styleOverrides: {\n          root: {\n            boxShadow: isDarkMode \n              ? '0 2px 8px rgba(0,0,0,0.3)' \n              : '0 2px 8px rgba(0,0,0,0.1)',\n          },\n        },\n      },\n      MuiButton: {\n        styleOverrides: {\n          root: {\n            textTransform: 'none',\n            fontWeight: 500,\n          },\n        },\n      },\n      MuiAppBar: {\n        styleOverrides: {\n          root: {\n            backgroundColor: isDarkMode ? '#1e1e1e' : '#ffffff',\n            color: isDarkMode ? '#ffffff' : '#000000',\n          },\n        },\n      },\n    },\n  });\n\n  const contextValue: ThemeContextType = {\n    themeMode,\n    setThemeMode,\n    isDarkMode,\n  };\n\n  return (\n    <ThemeContext.Provider value={contextValue}>\n      <ThemeProvider theme={theme}>\n        <CssBaseline />\n        {children}\n      </ThemeProvider>\n    </ThemeContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA;;AAEA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAmB,OAAO;AACxF,SAASC,aAAa,EAAEC,WAAW,QAAQ,sBAAsB;AACjE,SAASC,WAAW,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAU5C,MAAMC,YAAY,gBAAGT,aAAa,CAA+BU,SAAS,CAAC;AAE3E,OAAO,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,OAAO,GAAGZ,UAAU,CAACQ,YAAY,CAAC;EACxC,IAAI,CAACI,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,8CAA8C,CAAC;EACjE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,QAAQ;AAYrB,OAAO,MAAMI,mBAAuD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EACvF,MAAM,CAACC,SAAS,EAAEC,iBAAiB,CAAC,GAAGjB,QAAQ,CAAY,MAAM;IAC/D,MAAMkB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,OAAQF,KAAK,IAAkB,OAAO;EACxC,CAAC,CAAC;EAEF,MAAM,CAACG,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAMuB,cAAc,GAAGA,CAAA,KAAM;IAC3B,OAAOC,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC,CAACC,OAAO,GAAG,MAAM,GAAG,OAAO;EACrF,CAAC;;EAED;EACAzB,SAAS,CAAC,MAAM;IACd,IAAI0B,WAA6B;IAEjC,IAAIX,SAAS,KAAK,MAAM,EAAE;MACxBW,WAAW,GAAGJ,cAAc,CAAC,CAAC;IAChC,CAAC,MAAM;MACLI,WAAW,GAAGX,SAAS;IACzB;IAEAM,aAAa,CAACK,WAAW,KAAK,MAAM,CAAC;EACvC,CAAC,EAAE,CAACX,SAAS,CAAC,CAAC;;EAEf;EACAf,SAAS,CAAC,MAAM;IACd,IAAIe,SAAS,KAAK,MAAM,EAAE;MACxB,MAAMY,UAAU,GAAGJ,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC;MACpE,MAAMI,YAAY,GAAGA,CAAA,KAAM;QACzBP,aAAa,CAACM,UAAU,CAACF,OAAO,CAAC;MACnC,CAAC;MAEDE,UAAU,CAACE,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;MACnD,OAAO,MAAMD,UAAU,CAACG,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;IACrE;EACF,CAAC,EAAE,CAACb,SAAS,CAAC,CAAC;EAEf,MAAMgB,YAAY,GAAIC,IAAe,IAAK;IACxChB,iBAAiB,CAACgB,IAAI,CAAC;IACvBd,YAAY,CAACe,OAAO,CAAC,YAAY,EAAED,IAAI,CAAC;EAC1C,CAAC;;EAED;EACA,MAAME,KAAK,GAAGhC,WAAW,CAAC;IACxBiC,OAAO,EAAE;MACPH,IAAI,EAAEZ,UAAU,GAAG,MAAM,GAAG,OAAO;MACnCgB,OAAO,EAAE;QACPC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE;MACR,CAAC;MACDC,SAAS,EAAE;QACTH,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE;MACR,CAAC;MACDE,UAAU,EAAE;QACVC,OAAO,EAAEtB,UAAU,GAAG,SAAS,GAAG,SAAS;QAC3CuB,KAAK,EAAEvB,UAAU,GAAG,SAAS,GAAG;MAClC,CAAC;MACDwB,IAAI,EAAE;QACJR,OAAO,EAAEhB,UAAU,GAAG,SAAS,GAAG,SAAS;QAC3CoB,SAAS,EAAEpB,UAAU,GAAG,SAAS,GAAG;MACtC;IACF,CAAC;IACDyB,UAAU,EAAE;MACVC,UAAU,EAAE,qDAAqD;MACjEC,EAAE,EAAE;QACFC,UAAU,EAAE;MACd,CAAC;MACDC,EAAE,EAAE;QACFD,UAAU,EAAE;MACd,CAAC;MACDE,EAAE,EAAE;QACFF,UAAU,EAAE;MACd,CAAC;MACDG,EAAE,EAAE;QACFH,UAAU,EAAE;MACd,CAAC;MACDI,EAAE,EAAE;QACFJ,UAAU,EAAE;MACd,CAAC;MACDK,EAAE,EAAE;QACFL,UAAU,EAAE;MACd;IACF,CAAC;IACDM,KAAK,EAAE;MACLC,YAAY,EAAE;IAChB,CAAC;IACDC,UAAU,EAAE;MACVC,OAAO,EAAE;QACPC,cAAc,EAAE;UACdC,IAAI,EAAE;YACJC,SAAS,EAAExC,UAAU,GACjB,2BAA2B,GAC3B;UACN;QACF;MACF,CAAC;MACDyC,SAAS,EAAE;QACTH,cAAc,EAAE;UACdC,IAAI,EAAE;YACJG,aAAa,EAAE,MAAM;YACrBd,UAAU,EAAE;UACd;QACF;MACF,CAAC;MACDe,SAAS,EAAE;QACTL,cAAc,EAAE;UACdC,IAAI,EAAE;YACJK,eAAe,EAAE5C,UAAU,GAAG,SAAS,GAAG,SAAS;YACnD6C,KAAK,EAAE7C,UAAU,GAAG,SAAS,GAAG;UAClC;QACF;MACF;IACF;EACF,CAAC,CAAC;EAEF,MAAM8C,YAA8B,GAAG;IACrCnD,SAAS;IACTgB,YAAY;IACZX;EACF,CAAC;EAED,oBACEf,OAAA,CAACC,YAAY,CAAC6D,QAAQ;IAACC,KAAK,EAAEF,YAAa;IAAArD,QAAA,eACzCR,OAAA,CAACJ,aAAa;MAACiC,KAAK,EAAEA,KAAM;MAAArB,QAAA,gBAC1BR,OAAA,CAACF,WAAW;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACd3D,QAAQ;IAAA;MAAAwD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAE5B,CAAC;AAAC1D,GAAA,CAtIWF,mBAAuD;AAAA6D,EAAA,GAAvD7D,mBAAuD;AAAA,IAAA6D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}