{"ast": null, "code": "// TrustVault - API Service\n\nimport axios from 'axios';\nimport { toast } from 'react-hot-toast';\n\n// Types\n\nclass ApiService {\n  constructor() {\n    this.api = void 0;\n    this.baseURL = void 0;\n    // Authentication endpoints\n    this.login = async credentials => {\n      const response = await this.api.post('/auth/login/', credentials);\n      if (response.data.access_token) {\n        this.setTokens(response.data.access_token, response.data.refresh_token);\n      }\n      return response.data;\n    };\n    this.register = async data => {\n      const response = await this.api.post('/auth/register/', data);\n      return response.data;\n    };\n    this.logout = async () => {\n      const refreshToken = this.getRefreshToken();\n      try {\n        await this.api.post('/auth/logout/', {\n          refresh_token: refreshToken\n        });\n      } finally {\n        this.clearTokens();\n      }\n    };\n    this.getCurrentUser = async () => {\n      const response = await this.api.get('/auth/profile/');\n      return response.data;\n    };\n    this.updateProfile = async data => {\n      const response = await this.api.put('/auth/profile/', data);\n      return response.data;\n    };\n    this.changePassword = async data => {\n      const response = await this.api.post('/auth/change-password/', data);\n      return response.data;\n    };\n    // Portfolio endpoints\n    this.getPortfolios = async () => {\n      const response = await this.api.get('/portfolio/');\n      return response.data;\n    };\n    this.getPortfolio = async id => {\n      const response = await this.api.get(`/portfolio/${id}/`);\n      return response.data;\n    };\n    this.createPortfolio = async data => {\n      const response = await this.api.post('/portfolio/', data);\n      return response.data;\n    };\n    this.updatePortfolio = async (id, data) => {\n      const response = await this.api.put(`/portfolio/${id}/`, data);\n      return response.data;\n    };\n    this.deletePortfolio = async id => {\n      await this.api.delete(`/portfolio/${id}/`);\n    };\n    // Asset endpoints\n    this.getAssets = async params => {\n      const response = await this.api.get('/portfolio/assets/', {\n        params\n      });\n      return response.data;\n    };\n    // Holdings endpoints\n    this.getHoldings = async portfolioId => {\n      const response = await this.api.get(`/portfolio/${portfolioId}/holdings/`);\n      return response.data;\n    };\n    this.createHolding = async (portfolioId, data) => {\n      const response = await this.api.post(`/portfolio/${portfolioId}/holdings/`, data);\n      return response.data;\n    };\n    // Transaction endpoints\n    this.getTransactions = async portfolioId => {\n      const response = await this.api.get(`/portfolio/${portfolioId}/transactions/`);\n      return response.data;\n    };\n    this.createTransaction = async (portfolioId, data) => {\n      const response = await this.api.post(`/portfolio/${portfolioId}/transactions/`, data);\n      return response.data;\n    };\n    // Analytics endpoints\n    this.getPortfolioAnalytics = async portfolioId => {\n      const response = await this.api.get(`/portfolio/${portfolioId}/analytics/`);\n      return response.data;\n    };\n    // Security endpoints\n    this.getSecurityDashboard = async () => {\n      const response = await this.api.get('/security/dashboard/');\n      return response.data;\n    };\n    this.getSecurityEvents = async params => {\n      const response = await this.api.get('/security/events/', {\n        params\n      });\n      return response.data;\n    };\n    this.getAuditLogs = async params => {\n      const response = await this.api.get('/security/audit-logs/', {\n        params\n      });\n      return response.data;\n    };\n    // Health check\n    this.healthCheck = async () => {\n      const response = await this.api.get('/core/status/');\n      return response.data;\n    };\n    // Generic request method\n    this.request = async config => {\n      const response = await this.api.request(config);\n      return response.data;\n    };\n    this.baseURL = process.env.REACT_APP_API_URL || '/api/v1';\n    this.api = axios.create({\n      baseURL: this.baseURL,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n    this.setupInterceptors();\n  }\n  setupInterceptors() {\n    // Request interceptor\n    this.api.interceptors.request.use(config => {\n      // Add auth token if available\n      const token = this.getAccessToken();\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n\n      // Add security headers\n      config.headers['X-Requested-With'] = 'XMLHttpRequest';\n      config.headers['X-Client-Version'] = '1.0.0';\n\n      // Log request in development\n      if (process.env.NODE_ENV === 'development') {\n        var _config$method;\n        console.log(`API Request: ${(_config$method = config.method) === null || _config$method === void 0 ? void 0 : _config$method.toUpperCase()} ${config.url}`);\n      }\n      return config;\n    }, error => {\n      return Promise.reject(error);\n    });\n\n    // Response interceptor\n    this.api.interceptors.response.use(response => {\n      return response;\n    }, async error => {\n      var _error$response;\n      const originalRequest = error.config;\n\n      // Handle 401 errors (unauthorized)\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401 && !originalRequest._retry) {\n        originalRequest._retry = true;\n        try {\n          // Try to refresh token\n          const refreshToken = this.getRefreshToken();\n          if (refreshToken) {\n            const response = await this.refreshAccessToken(refreshToken);\n            this.setTokens(response.data.access_token, refreshToken);\n\n            // Retry original request\n            originalRequest.headers.Authorization = `Bearer ${response.data.access_token}`;\n            return this.api(originalRequest);\n          }\n        } catch (refreshError) {\n          // Refresh failed, redirect to login\n          this.clearTokens();\n          window.location.href = '/login';\n          return Promise.reject(refreshError);\n        }\n      }\n\n      // Handle other errors\n      this.handleApiError(error);\n      return Promise.reject(error);\n    });\n  }\n  handleApiError(error) {\n    var _error$response2, _error$response2$data, _error$response3;\n    const message = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || error.message || 'An error occurred';\n\n    // Don't show toast for certain errors\n    const silentErrors = [401, 403];\n    if (!silentErrors.includes((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.status)) {\n      toast.error(message);\n    }\n\n    // Log error in development\n    if (process.env.NODE_ENV === 'development') {\n      var _error$response4;\n      console.error('API Error:', ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.data) || error.message);\n    }\n  }\n\n  // Token management\n  getAccessToken() {\n    return localStorage.getItem('access_token');\n  }\n  getRefreshToken() {\n    return localStorage.getItem('refresh_token');\n  }\n  setTokens(accessToken, refreshToken) {\n    localStorage.setItem('access_token', accessToken);\n    localStorage.setItem('refresh_token', refreshToken);\n  }\n  clearTokens() {\n    localStorage.removeItem('access_token');\n    localStorage.removeItem('refresh_token');\n  }\n  async refreshAccessToken(refreshToken) {\n    return this.api.post('/auth/token/refresh/', {\n      refresh: refreshToken\n    });\n  }\n}\n\n// Create singleton instance\nconst apiService = new ApiService();\nexport default apiService;", "map": {"version": 3, "names": ["axios", "toast", "ApiService", "constructor", "api", "baseURL", "login", "credentials", "response", "post", "data", "access_token", "setTokens", "refresh_token", "register", "logout", "refreshToken", "getRefreshToken", "clearTokens", "getCurrentUser", "get", "updateProfile", "put", "changePassword", "getPortfolios", "getPortfolio", "id", "createPortfolio", "updatePortfolio", "deletePortfolio", "delete", "getAssets", "params", "getHoldings", "portfolioId", "createHolding", "getTransactions", "createTransaction", "getPortfolioAnalytics", "getSecurityDashboard", "getSecurityEvents", "getAuditLogs", "healthCheck", "request", "config", "process", "env", "REACT_APP_API_URL", "create", "timeout", "headers", "setupInterceptors", "interceptors", "use", "token", "getAccessToken", "Authorization", "NODE_ENV", "_config$method", "console", "log", "method", "toUpperCase", "url", "error", "Promise", "reject", "_error$response", "originalRequest", "status", "_retry", "refreshAccessToken", "refreshError", "window", "location", "href", "handleApiError", "_error$response2", "_error$response2$data", "_error$response3", "message", "silentErrors", "includes", "_error$response4", "localStorage", "getItem", "accessToken", "setItem", "removeItem", "refresh", "apiService"], "sources": ["C:/Users/<USER>/Documents/augment-projects/TrustVault/frontend/src/services/api.ts"], "sourcesContent": ["// TrustVault - API Service\n\nimport axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';\nimport { toast } from 'react-hot-toast';\n\n// Types\nimport {\n  AuthResponse,\n  LoginCredentials,\n  RegisterData,\n  User,\n  Portfolio,\n  Asset,\n  Transaction,\n  SecurityDashboard,\n  ChangePasswordData,\n  PaginatedResponse,\n} from '../types';\n\nclass ApiService {\n  private api: AxiosInstance;\n  private baseURL: string;\n\n  constructor() {\n    this.baseURL = process.env.REACT_APP_API_URL || '/api/v1';\n    \n    this.api = axios.create({\n      baseURL: this.baseURL,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    this.setupInterceptors();\n  }\n\n  private setupInterceptors(): void {\n    // Request interceptor\n    this.api.interceptors.request.use(\n      (config) => {\n        // Add auth token if available\n        const token = this.getAccessToken();\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n\n        // Add security headers\n        config.headers['X-Requested-With'] = 'XMLHttpRequest';\n        config.headers['X-Client-Version'] = '1.0.0';\n\n        // Log request in development\n        if (process.env.NODE_ENV === 'development') {\n          console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);\n        }\n\n        return config;\n      },\n      (error) => {\n        return Promise.reject(error);\n      }\n    );\n\n    // Response interceptor\n    this.api.interceptors.response.use(\n      (response: AxiosResponse) => {\n        return response;\n      },\n      async (error) => {\n        const originalRequest = error.config;\n\n        // Handle 401 errors (unauthorized)\n        if (error.response?.status === 401 && !originalRequest._retry) {\n          originalRequest._retry = true;\n\n          try {\n            // Try to refresh token\n            const refreshToken = this.getRefreshToken();\n            if (refreshToken) {\n              const response = await this.refreshAccessToken(refreshToken);\n              this.setTokens(response.data.access_token, refreshToken);\n              \n              // Retry original request\n              originalRequest.headers.Authorization = `Bearer ${response.data.access_token}`;\n              return this.api(originalRequest);\n            }\n          } catch (refreshError) {\n            // Refresh failed, redirect to login\n            this.clearTokens();\n            window.location.href = '/login';\n            return Promise.reject(refreshError);\n          }\n        }\n\n        // Handle other errors\n        this.handleApiError(error);\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  private handleApiError(error: any): void {\n    const message = error.response?.data?.message || error.message || 'An error occurred';\n    \n    // Don't show toast for certain errors\n    const silentErrors = [401, 403];\n    if (!silentErrors.includes(error.response?.status)) {\n      toast.error(message);\n    }\n\n    // Log error in development\n    if (process.env.NODE_ENV === 'development') {\n      console.error('API Error:', error.response?.data || error.message);\n    }\n  }\n\n  // Token management\n  private getAccessToken(): string | null {\n    return localStorage.getItem('access_token');\n  }\n\n  private getRefreshToken(): string | null {\n    return localStorage.getItem('refresh_token');\n  }\n\n  private setTokens(accessToken: string, refreshToken: string): void {\n    localStorage.setItem('access_token', accessToken);\n    localStorage.setItem('refresh_token', refreshToken);\n  }\n\n  private clearTokens(): void {\n    localStorage.removeItem('access_token');\n    localStorage.removeItem('refresh_token');\n  }\n\n  private async refreshAccessToken(refreshToken: string): Promise<AxiosResponse> {\n    return this.api.post('/auth/token/refresh/', {\n      refresh: refreshToken,\n    });\n  }\n\n  // Authentication endpoints\n  login = async (credentials: LoginCredentials): Promise<AuthResponse> => {\n    const response = await this.api.post<AuthResponse>('/auth/login/', credentials);\n\n    if (response.data.access_token) {\n      this.setTokens(response.data.access_token, response.data.refresh_token);\n    }\n\n    return response.data;\n  }\n\n  register = async (data: RegisterData): Promise<{ message: string; user_id: string }> => {\n    const response = await this.api.post('/auth/register/', data);\n    return response.data;\n  }\n\n  logout = async (): Promise<void> => {\n    const refreshToken = this.getRefreshToken();\n\n    try {\n      await this.api.post('/auth/logout/', {\n        refresh_token: refreshToken,\n      });\n    } finally {\n      this.clearTokens();\n    }\n  }\n\n  getCurrentUser = async (): Promise<User> => {\n    const response = await this.api.get<User>('/auth/profile/');\n    return response.data;\n  }\n\n  updateProfile = async (data: Partial<User>): Promise<User> => {\n    const response = await this.api.put<User>('/auth/profile/', data);\n    return response.data;\n  }\n\n  changePassword = async (data: ChangePasswordData): Promise<{ message: string }> => {\n    const response = await this.api.post('/auth/change-password/', data);\n    return response.data;\n  }\n\n  // Portfolio endpoints\n  getPortfolios = async (): Promise<Portfolio[]> => {\n    const response = await this.api.get<Portfolio[]>('/portfolio/');\n    return response.data;\n  }\n\n  getPortfolio = async (id: string): Promise<Portfolio> => {\n    const response = await this.api.get<Portfolio>(`/portfolio/${id}/`);\n    return response.data;\n  }\n\n  createPortfolio = async (data: Partial<Portfolio>): Promise<Portfolio> => {\n    const response = await this.api.post<Portfolio>('/portfolio/', data);\n    return response.data;\n  }\n\n  updatePortfolio = async (id: string, data: Partial<Portfolio>): Promise<Portfolio> => {\n    const response = await this.api.put<Portfolio>(`/portfolio/${id}/`, data);\n    return response.data;\n  }\n\n  deletePortfolio = async (id: string): Promise<void> => {\n    await this.api.delete(`/portfolio/${id}/`);\n  }\n\n  // Asset endpoints\n  getAssets = async (params?: {\n    type?: string;\n    search?: string;\n    sector?: string;\n  }): Promise<Asset[]> => {\n    const response = await this.api.get<Asset[]>('/portfolio/assets/', { params });\n    return response.data;\n  }\n\n  // Holdings endpoints\n  getHoldings = async (portfolioId: string): Promise<any[]> => {\n    const response = await this.api.get<any[]>(`/portfolio/${portfolioId}/holdings/`);\n    return response.data;\n  }\n\n  createHolding = async (portfolioId: string, data: any): Promise<any> => {\n    const response = await this.api.post<any>(`/portfolio/${portfolioId}/holdings/`, data);\n    return response.data;\n  }\n\n  // Transaction endpoints\n  getTransactions = async (portfolioId: string): Promise<Transaction[]> => {\n    const response = await this.api.get<Transaction[]>(`/portfolio/${portfolioId}/transactions/`);\n    return response.data;\n  }\n\n  createTransaction = async (portfolioId: string, data: Partial<Transaction>): Promise<Transaction> => {\n    const response = await this.api.post<Transaction>(`/portfolio/${portfolioId}/transactions/`, data);\n    return response.data;\n  }\n\n  // Analytics endpoints\n  getPortfolioAnalytics = async (portfolioId: string): Promise<any> => {\n    const response = await this.api.get(`/portfolio/${portfolioId}/analytics/`);\n    return response.data;\n  }\n\n  // Security endpoints\n  getSecurityDashboard = async (): Promise<SecurityDashboard> => {\n    const response = await this.api.get<SecurityDashboard>('/security/dashboard/');\n    return response.data;\n  }\n\n  getSecurityEvents = async (params?: {\n    risk_level?: string;\n    event_type?: string;\n    is_resolved?: boolean;\n    page?: number;\n  }): Promise<PaginatedResponse<any>> => {\n    const response = await this.api.get('/security/events/', { params });\n    return response.data;\n  }\n\n  getAuditLogs = async (params?: {\n    action?: string;\n    resource_type?: string;\n    user_id?: string;\n    severity?: string;\n    page?: number;\n  }): Promise<PaginatedResponse<any>> => {\n    const response = await this.api.get('/security/audit-logs/', { params });\n    return response.data;\n  }\n\n  // Health check\n  healthCheck = async (): Promise<{ status: string }> => {\n    const response = await this.api.get('/core/status/');\n    return response.data;\n  }\n\n  // Generic request method\n  request = async <T>(config: AxiosRequestConfig): Promise<T> => {\n    const response = await this.api.request<T>(config);\n    return response.data;\n  }\n}\n\n// Create singleton instance\nconst apiService = new ApiService();\n\nexport default apiService;\n"], "mappings": "AAAA;;AAEA,OAAOA,KAAK,MAA4D,OAAO;AAC/E,SAASC,KAAK,QAAQ,iBAAiB;;AAEvC;;AAcA,MAAMC,UAAU,CAAC;EAIfC,WAAWA,CAAA,EAAG;IAAA,KAHNC,GAAG;IAAA,KACHC,OAAO;IAwHf;IAAA,KACAC,KAAK,GAAG,MAAOC,WAA6B,IAA4B;MACtE,MAAMC,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACK,IAAI,CAAe,cAAc,EAAEF,WAAW,CAAC;MAE/E,IAAIC,QAAQ,CAACE,IAAI,CAACC,YAAY,EAAE;QAC9B,IAAI,CAACC,SAAS,CAACJ,QAAQ,CAACE,IAAI,CAACC,YAAY,EAAEH,QAAQ,CAACE,IAAI,CAACG,aAAa,CAAC;MACzE;MAEA,OAAOL,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAEDI,QAAQ,GAAG,MAAOJ,IAAkB,IAAoD;MACtF,MAAMF,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACK,IAAI,CAAC,iBAAiB,EAAEC,IAAI,CAAC;MAC7D,OAAOF,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAEDK,MAAM,GAAG,YAA2B;MAClC,MAAMC,YAAY,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;MAE3C,IAAI;QACF,MAAM,IAAI,CAACb,GAAG,CAACK,IAAI,CAAC,eAAe,EAAE;UACnCI,aAAa,EAAEG;QACjB,CAAC,CAAC;MACJ,CAAC,SAAS;QACR,IAAI,CAACE,WAAW,CAAC,CAAC;MACpB;IACF,CAAC;IAAA,KAEDC,cAAc,GAAG,YAA2B;MAC1C,MAAMX,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACgB,GAAG,CAAO,gBAAgB,CAAC;MAC3D,OAAOZ,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAEDW,aAAa,GAAG,MAAOX,IAAmB,IAAoB;MAC5D,MAAMF,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACkB,GAAG,CAAO,gBAAgB,EAAEZ,IAAI,CAAC;MACjE,OAAOF,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAEDa,cAAc,GAAG,MAAOb,IAAwB,IAAmC;MACjF,MAAMF,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACK,IAAI,CAAC,wBAAwB,EAAEC,IAAI,CAAC;MACpE,OAAOF,QAAQ,CAACE,IAAI;IACtB,CAAC;IAED;IAAA,KACAc,aAAa,GAAG,YAAkC;MAChD,MAAMhB,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACgB,GAAG,CAAc,aAAa,CAAC;MAC/D,OAAOZ,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAEDe,YAAY,GAAG,MAAOC,EAAU,IAAyB;MACvD,MAAMlB,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACgB,GAAG,CAAY,cAAcM,EAAE,GAAG,CAAC;MACnE,OAAOlB,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAEDiB,eAAe,GAAG,MAAOjB,IAAwB,IAAyB;MACxE,MAAMF,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACK,IAAI,CAAY,aAAa,EAAEC,IAAI,CAAC;MACpE,OAAOF,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAEDkB,eAAe,GAAG,OAAOF,EAAU,EAAEhB,IAAwB,KAAyB;MACpF,MAAMF,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACkB,GAAG,CAAY,cAAcI,EAAE,GAAG,EAAEhB,IAAI,CAAC;MACzE,OAAOF,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAEDmB,eAAe,GAAG,MAAOH,EAAU,IAAoB;MACrD,MAAM,IAAI,CAACtB,GAAG,CAAC0B,MAAM,CAAC,cAAcJ,EAAE,GAAG,CAAC;IAC5C,CAAC;IAED;IAAA,KACAK,SAAS,GAAG,MAAOC,MAIlB,IAAuB;MACtB,MAAMxB,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACgB,GAAG,CAAU,oBAAoB,EAAE;QAAEY;MAAO,CAAC,CAAC;MAC9E,OAAOxB,QAAQ,CAACE,IAAI;IACtB,CAAC;IAED;IAAA,KACAuB,WAAW,GAAG,MAAOC,WAAmB,IAAqB;MAC3D,MAAM1B,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACgB,GAAG,CAAQ,cAAcc,WAAW,YAAY,CAAC;MACjF,OAAO1B,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAEDyB,aAAa,GAAG,OAAOD,WAAmB,EAAExB,IAAS,KAAmB;MACtE,MAAMF,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACK,IAAI,CAAM,cAAcyB,WAAW,YAAY,EAAExB,IAAI,CAAC;MACtF,OAAOF,QAAQ,CAACE,IAAI;IACtB,CAAC;IAED;IAAA,KACA0B,eAAe,GAAG,MAAOF,WAAmB,IAA6B;MACvE,MAAM1B,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACgB,GAAG,CAAgB,cAAcc,WAAW,gBAAgB,CAAC;MAC7F,OAAO1B,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAED2B,iBAAiB,GAAG,OAAOH,WAAmB,EAAExB,IAA0B,KAA2B;MACnG,MAAMF,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACK,IAAI,CAAc,cAAcyB,WAAW,gBAAgB,EAAExB,IAAI,CAAC;MAClG,OAAOF,QAAQ,CAACE,IAAI;IACtB,CAAC;IAED;IAAA,KACA4B,qBAAqB,GAAG,MAAOJ,WAAmB,IAAmB;MACnE,MAAM1B,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACgB,GAAG,CAAC,cAAcc,WAAW,aAAa,CAAC;MAC3E,OAAO1B,QAAQ,CAACE,IAAI;IACtB,CAAC;IAED;IAAA,KACA6B,oBAAoB,GAAG,YAAwC;MAC7D,MAAM/B,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACgB,GAAG,CAAoB,sBAAsB,CAAC;MAC9E,OAAOZ,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAED8B,iBAAiB,GAAG,MAAOR,MAK1B,IAAsC;MACrC,MAAMxB,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACgB,GAAG,CAAC,mBAAmB,EAAE;QAAEY;MAAO,CAAC,CAAC;MACpE,OAAOxB,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAED+B,YAAY,GAAG,MAAOT,MAMrB,IAAsC;MACrC,MAAMxB,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACgB,GAAG,CAAC,uBAAuB,EAAE;QAAEY;MAAO,CAAC,CAAC;MACxE,OAAOxB,QAAQ,CAACE,IAAI;IACtB,CAAC;IAED;IAAA,KACAgC,WAAW,GAAG,YAAyC;MACrD,MAAMlC,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACgB,GAAG,CAAC,eAAe,CAAC;MACpD,OAAOZ,QAAQ,CAACE,IAAI;IACtB,CAAC;IAED;IAAA,KACAiC,OAAO,GAAG,MAAUC,MAA0B,IAAiB;MAC7D,MAAMpC,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACuC,OAAO,CAAIC,MAAM,CAAC;MAClD,OAAOpC,QAAQ,CAACE,IAAI;IACtB,CAAC;IApQC,IAAI,CAACL,OAAO,GAAGwC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,SAAS;IAEzD,IAAI,CAAC3C,GAAG,GAAGJ,KAAK,CAACgD,MAAM,CAAC;MACtB3C,OAAO,EAAE,IAAI,CAACA,OAAO;MACrB4C,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IAEF,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC1B;EAEQA,iBAAiBA,CAAA,EAAS;IAChC;IACA,IAAI,CAAC/C,GAAG,CAACgD,YAAY,CAACT,OAAO,CAACU,GAAG,CAC9BT,MAAM,IAAK;MACV;MACA,MAAMU,KAAK,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACnC,IAAID,KAAK,EAAE;QACTV,MAAM,CAACM,OAAO,CAACM,aAAa,GAAG,UAAUF,KAAK,EAAE;MAClD;;MAEA;MACAV,MAAM,CAACM,OAAO,CAAC,kBAAkB,CAAC,GAAG,gBAAgB;MACrDN,MAAM,CAACM,OAAO,CAAC,kBAAkB,CAAC,GAAG,OAAO;;MAE5C;MACA,IAAIL,OAAO,CAACC,GAAG,CAACW,QAAQ,KAAK,aAAa,EAAE;QAAA,IAAAC,cAAA;QAC1CC,OAAO,CAACC,GAAG,CAAC,iBAAAF,cAAA,GAAgBd,MAAM,CAACiB,MAAM,cAAAH,cAAA,uBAAbA,cAAA,CAAeI,WAAW,CAAC,CAAC,IAAIlB,MAAM,CAACmB,GAAG,EAAE,CAAC;MAC3E;MAEA,OAAOnB,MAAM;IACf,CAAC,EACAoB,KAAK,IAAK;MACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;;IAED;IACA,IAAI,CAAC5D,GAAG,CAACgD,YAAY,CAAC5C,QAAQ,CAAC6C,GAAG,CAC/B7C,QAAuB,IAAK;MAC3B,OAAOA,QAAQ;IACjB,CAAC,EACD,MAAOwD,KAAK,IAAK;MAAA,IAAAG,eAAA;MACf,MAAMC,eAAe,GAAGJ,KAAK,CAACpB,MAAM;;MAEpC;MACA,IAAI,EAAAuB,eAAA,GAAAH,KAAK,CAACxD,QAAQ,cAAA2D,eAAA,uBAAdA,eAAA,CAAgBE,MAAM,MAAK,GAAG,IAAI,CAACD,eAAe,CAACE,MAAM,EAAE;QAC7DF,eAAe,CAACE,MAAM,GAAG,IAAI;QAE7B,IAAI;UACF;UACA,MAAMtD,YAAY,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;UAC3C,IAAID,YAAY,EAAE;YAChB,MAAMR,QAAQ,GAAG,MAAM,IAAI,CAAC+D,kBAAkB,CAACvD,YAAY,CAAC;YAC5D,IAAI,CAACJ,SAAS,CAACJ,QAAQ,CAACE,IAAI,CAACC,YAAY,EAAEK,YAAY,CAAC;;YAExD;YACAoD,eAAe,CAAClB,OAAO,CAACM,aAAa,GAAG,UAAUhD,QAAQ,CAACE,IAAI,CAACC,YAAY,EAAE;YAC9E,OAAO,IAAI,CAACP,GAAG,CAACgE,eAAe,CAAC;UAClC;QACF,CAAC,CAAC,OAAOI,YAAY,EAAE;UACrB;UACA,IAAI,CAACtD,WAAW,CAAC,CAAC;UAClBuD,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;UAC/B,OAAOV,OAAO,CAACC,MAAM,CAACM,YAAY,CAAC;QACrC;MACF;;MAEA;MACA,IAAI,CAACI,cAAc,CAACZ,KAAK,CAAC;MAC1B,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;EACH;EAEQY,cAAcA,CAACZ,KAAU,EAAQ;IAAA,IAAAa,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA;IACvC,MAAMC,OAAO,GAAG,EAAAH,gBAAA,GAAAb,KAAK,CAACxD,QAAQ,cAAAqE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnE,IAAI,cAAAoE,qBAAA,uBAApBA,qBAAA,CAAsBE,OAAO,KAAIhB,KAAK,CAACgB,OAAO,IAAI,mBAAmB;;IAErF;IACA,MAAMC,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;IAC/B,IAAI,CAACA,YAAY,CAACC,QAAQ,EAAAH,gBAAA,GAACf,KAAK,CAACxD,QAAQ,cAAAuE,gBAAA,uBAAdA,gBAAA,CAAgBV,MAAM,CAAC,EAAE;MAClDpE,KAAK,CAAC+D,KAAK,CAACgB,OAAO,CAAC;IACtB;;IAEA;IACA,IAAInC,OAAO,CAACC,GAAG,CAACW,QAAQ,KAAK,aAAa,EAAE;MAAA,IAAA0B,gBAAA;MAC1CxB,OAAO,CAACK,KAAK,CAAC,YAAY,EAAE,EAAAmB,gBAAA,GAAAnB,KAAK,CAACxD,QAAQ,cAAA2E,gBAAA,uBAAdA,gBAAA,CAAgBzE,IAAI,KAAIsD,KAAK,CAACgB,OAAO,CAAC;IACpE;EACF;;EAEA;EACQzB,cAAcA,CAAA,EAAkB;IACtC,OAAO6B,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;EAC7C;EAEQpE,eAAeA,CAAA,EAAkB;IACvC,OAAOmE,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;EAC9C;EAEQzE,SAASA,CAAC0E,WAAmB,EAAEtE,YAAoB,EAAQ;IACjEoE,YAAY,CAACG,OAAO,CAAC,cAAc,EAAED,WAAW,CAAC;IACjDF,YAAY,CAACG,OAAO,CAAC,eAAe,EAAEvE,YAAY,CAAC;EACrD;EAEQE,WAAWA,CAAA,EAAS;IAC1BkE,YAAY,CAACI,UAAU,CAAC,cAAc,CAAC;IACvCJ,YAAY,CAACI,UAAU,CAAC,eAAe,CAAC;EAC1C;EAEA,MAAcjB,kBAAkBA,CAACvD,YAAoB,EAA0B;IAC7E,OAAO,IAAI,CAACZ,GAAG,CAACK,IAAI,CAAC,sBAAsB,EAAE;MAC3CgF,OAAO,EAAEzE;IACX,CAAC,CAAC;EACJ;AAkJF;;AAEA;AACA,MAAM0E,UAAU,GAAG,IAAIxF,UAAU,CAAC,CAAC;AAEnC,eAAewF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}