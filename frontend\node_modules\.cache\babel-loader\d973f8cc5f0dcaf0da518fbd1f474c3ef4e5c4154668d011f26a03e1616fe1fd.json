{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\TrustVault\\\\frontend\\\\src\\\\pages\\\\Settings\\\\SettingsPage.tsx\",\n  _s = $RefreshSig$();\n// TrustVault - Settings Page\n\nimport React, { useState } from 'react';\nimport { Box, Typography, Grid, Card, CardContent, FormControl, InputLabel, Select, MenuItem, Switch, Button, Alert, List, ListItem, ListItemText, ListItemSecondaryAction, Dialog, DialogTitle, DialogContent, DialogActions, TextField, CircularProgress, Chip, Divider } from '@mui/material';\nimport { Settings, Notifications, Security, Delete, Save, Download, Devices, Brightness4, Brightness7, BrightnessAuto } from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { toast } from 'react-hot-toast';\nimport { useMutation, useQueryClient, useQuery } from 'react-query';\n\n// Store\nimport { useAuthStore } from '../../store/authStore';\n\n// Contexts\nimport { useTheme } from '../../contexts/ThemeContext';\n\n// Services\nimport apiService from '../../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SettingsPage = () => {\n  _s();\n  const {\n    user\n  } = useAuthStore();\n  const queryClient = useQueryClient();\n  const {\n    themeMode,\n    setThemeMode,\n    isDarkMode\n  } = useTheme();\n  const [settings, setSettings] = useState({\n    timezone: (user === null || user === void 0 ? void 0 : user.timezone) || 'UTC',\n    language: (user === null || user === void 0 ? void 0 : user.language) || 'en',\n    theme: themeMode,\n    notifications: {\n      email: true,\n      push: true,\n      security: true,\n      portfolio: true,\n      marketing: false,\n      news: true,\n      email_frequency: 'daily',\n      push_enabled: true\n    },\n    privacy: {\n      profile_visibility: 'private',\n      portfolio_visibility: 'private',\n      data_sharing: false,\n      analytics: true\n    }\n  });\n  const [deleteAccountDialog, setDeleteAccountDialog] = useState(false);\n  const [deleteConfirmation, setDeleteConfirmation] = useState('');\n  const [sessionsDialog, setSessionsDialog] = useState(false);\n  const [exportingData, setExportingData] = useState(false);\n\n  // Load user settings\n  const {\n    data: userSettings,\n    isLoading: settingsLoading\n  } = useQuery('user-settings', apiService.getUserSettings, {\n    onSuccess: data => {\n      setSettings(prev => ({\n        ...prev,\n        ...data\n      }));\n    },\n    onError: () => {\n      // Use default settings if API fails\n    }\n  });\n\n  // Load active sessions\n  const {\n    data: activeSessions,\n    refetch: refetchSessions\n  } = useQuery('active-sessions', apiService.getActiveSessions, {\n    enabled: sessionsDialog,\n    onError: () => {\n      toast.error('Failed to load active sessions');\n    }\n  });\n  const updateSettingsMutation = useMutation(data => apiService.updateUserSettings(data), {\n    onSuccess: () => {\n      toast.success('Settings updated successfully');\n      queryClient.invalidateQueries(['user-settings', 'user-profile']);\n    },\n    onError: () => {\n      toast.error('Failed to update settings');\n    }\n  });\n  const deleteAccountMutation = useMutation(confirmation => apiService.deleteAccount(confirmation), {\n    onSuccess: () => {\n      toast.success('Account deleted successfully');\n      // Redirect to login or home page\n      window.location.href = '/';\n    },\n    onError: error => {\n      var _error$response, _error$response$data;\n      const message = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to delete account';\n      toast.error(message);\n    }\n  });\n  const revokeSessionMutation = useMutation(sessionId => apiService.revokeSession(sessionId), {\n    onSuccess: () => {\n      toast.success('Session revoked successfully');\n      refetchSessions();\n    },\n    onError: () => {\n      toast.error('Failed to revoke session');\n    }\n  });\n  const revokeAllSessionsMutation = useMutation(() => apiService.revokeAllSessions(), {\n    onSuccess: () => {\n      toast.success('All sessions revoked successfully');\n      refetchSessions();\n    },\n    onError: () => {\n      toast.error('Failed to revoke all sessions');\n    }\n  });\n  const handleSettingChange = (category, key, value) => {\n    setSettings(prev => ({\n      ...prev,\n      [category]: typeof prev[category] === 'object' && prev[category] !== null ? {\n        ...prev[category],\n        [key]: value\n      } : value\n    }));\n  };\n  const handleThemeChange = newTheme => {\n    setThemeMode(newTheme);\n    handleSettingChange('theme', '', newTheme);\n  };\n  const handleSaveSettings = () => {\n    updateSettingsMutation.mutate(settings);\n  };\n  const handleExportData = async () => {\n    setExportingData(true);\n    try {\n      const blob = await apiService.exportUserData();\n      const url = window.URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = `trustvault-data-${new Date().toISOString().split('T')[0]}.json`;\n      document.body.appendChild(a);\n      a.click();\n      window.URL.revokeObjectURL(url);\n      document.body.removeChild(a);\n      toast.success('Data exported successfully');\n    } catch (error) {\n      toast.error('Failed to export data');\n    } finally {\n      setExportingData(false);\n    }\n  };\n  const handleDeleteAccount = () => {\n    if (deleteConfirmation === 'DELETE') {\n      deleteAccountMutation.mutate(deleteConfirmation);\n    }\n  };\n  const timezones = [{\n    value: 'UTC',\n    label: 'UTC'\n  }, {\n    value: 'America/New_York',\n    label: 'Eastern Time'\n  }, {\n    value: 'America/Chicago',\n    label: 'Central Time'\n  }, {\n    value: 'America/Denver',\n    label: 'Mountain Time'\n  }, {\n    value: 'America/Los_Angeles',\n    label: 'Pacific Time'\n  }, {\n    value: 'Europe/London',\n    label: 'London'\n  }, {\n    value: 'Europe/Paris',\n    label: 'Paris'\n  }, {\n    value: 'Asia/Tokyo',\n    label: 'Tokyo'\n  }];\n  const languages = [{\n    value: 'en',\n    label: 'English'\n  }, {\n    value: 'fr',\n    label: 'Français'\n  }, {\n    value: 'es',\n    label: 'Español'\n  }, {\n    value: 'de',\n    label: 'Deutsch'\n  }];\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"Settings - TrustVault\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: \"Manage your account settings and preferences\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        mb: 4,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h1\",\n          gutterBottom: true,\n          children: \"Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          color: \"text.secondary\",\n          children: \"Manage your account preferences and security settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 2,\n                mb: 3,\n                children: [/*#__PURE__*/_jsxDEV(Settings, {\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: \"General\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                flexDirection: \"column\",\n                gap: 3,\n                children: [/*#__PURE__*/_jsxDEV(FormControl, {\n                  fullWidth: true,\n                  children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                    children: \"Timezone\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    value: settings.timezone,\n                    onChange: e => handleSettingChange('timezone', '', e.target.value),\n                    label: \"Timezone\",\n                    children: timezones.map(tz => /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: tz.value,\n                      children: tz.label\n                    }, tz.value, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 289,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 283,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                  fullWidth: true,\n                  children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                    children: \"Language\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 297,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    value: settings.language,\n                    onChange: e => handleSettingChange('language', '', e.target.value),\n                    label: \"Language\",\n                    children: languages.map(lang => /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: lang.value,\n                      children: lang.label\n                    }, lang.value, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 304,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 298,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                  fullWidth: true,\n                  children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                    children: \"Theme\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    value: themeMode,\n                    onChange: e => handleThemeChange(e.target.value),\n                    label: \"Theme\",\n                    children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"light\",\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: 1,\n                        children: [/*#__PURE__*/_jsxDEV(Brightness7, {\n                          fontSize: \"small\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 320,\n                          columnNumber: 27\n                        }, this), \"Light\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 319,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 318,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"dark\",\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: 1,\n                        children: [/*#__PURE__*/_jsxDEV(Brightness4, {\n                          fontSize: \"small\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 326,\n                          columnNumber: 27\n                        }, this), \"Dark\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 325,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 324,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"auto\",\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: 1,\n                        children: [/*#__PURE__*/_jsxDEV(BrightnessAuto, {\n                          fontSize: \"small\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 332,\n                          columnNumber: 27\n                        }, this), \"Auto (System)\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 331,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 330,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 2,\n                mb: 3,\n                children: [/*#__PURE__*/_jsxDEV(Notifications, {\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: \"Notifications\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                flexDirection: \"column\",\n                gap: 2,\n                mb: 3,\n                children: /*#__PURE__*/_jsxDEV(FormControl, {\n                  fullWidth: true,\n                  children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                    children: \"Email Frequency\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 354,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    value: settings.notifications.email_frequency,\n                    onChange: e => handleSettingChange('notifications', 'email_frequency', e.target.value),\n                    label: \"Email Frequency\",\n                    children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"immediate\",\n                      children: \"Immediate\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 360,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"daily\",\n                      children: \"Daily Digest\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 361,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"weekly\",\n                      children: \"Weekly Summary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 362,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"never\",\n                      children: \"Never\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 363,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 355,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(List, {\n                children: [/*#__PURE__*/_jsxDEV(ListItem, {\n                  children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: \"Email Notifications\",\n                    secondary: \"Receive updates via email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 370,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                    children: /*#__PURE__*/_jsxDEV(Switch, {\n                      checked: settings.notifications.email,\n                      onChange: e => handleSettingChange('notifications', 'email', e.target.checked)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 375,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                  children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: \"Push Notifications\",\n                    secondary: \"Browser push notifications\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 383,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                    children: /*#__PURE__*/_jsxDEV(Switch, {\n                      checked: settings.notifications.push,\n                      onChange: e => handleSettingChange('notifications', 'push', e.target.checked)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 388,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 387,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                  children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: \"Security Alerts\",\n                    secondary: \"Important security notifications (always enabled)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 396,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                    children: /*#__PURE__*/_jsxDEV(Switch, {\n                      checked: true,\n                      disabled: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 401,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 400,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                  children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: \"Portfolio Updates\",\n                    secondary: \"Portfolio performance notifications\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 409,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                    children: /*#__PURE__*/_jsxDEV(Switch, {\n                      checked: settings.notifications.portfolio,\n                      onChange: e => handleSettingChange('notifications', 'portfolio', e.target.checked)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 414,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 413,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                  children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: \"Marketing Communications\",\n                    secondary: \"Product updates and promotional content\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 422,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                    children: /*#__PURE__*/_jsxDEV(Switch, {\n                      checked: settings.notifications.marketing,\n                      onChange: e => handleSettingChange('notifications', 'marketing', e.target.checked)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 427,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 426,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                  children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: \"Market News\",\n                    secondary: \"Financial market news and insights\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 435,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                    children: /*#__PURE__*/_jsxDEV(Switch, {\n                      checked: settings.notifications.news,\n                      onChange: e => handleSettingChange('notifications', 'news', e.target.checked)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 440,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 439,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 2,\n                mb: 3,\n                children: [/*#__PURE__*/_jsxDEV(Security, {\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: \"Privacy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                flexDirection: \"column\",\n                gap: 3,\n                children: [/*#__PURE__*/_jsxDEV(FormControl, {\n                  fullWidth: true,\n                  children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                    children: \"Profile Visibility\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 462,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    value: settings.privacy.profile_visibility,\n                    onChange: e => handleSettingChange('privacy', 'profile_visibility', e.target.value),\n                    label: \"Profile Visibility\",\n                    children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"public\",\n                      children: \"Public\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 468,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"private\",\n                      children: \"Private\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 469,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 463,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                  fullWidth: true,\n                  children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                    children: \"Portfolio Visibility\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    value: settings.privacy.portfolio_visibility,\n                    onChange: e => handleSettingChange('privacy', 'portfolio_visibility', e.target.value),\n                    label: \"Portfolio Visibility\",\n                    children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"public\",\n                      children: \"Public\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 480,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"private\",\n                      children: \"Private\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 481,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 475,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 473,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {\n                sx: {\n                  my: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(List, {\n                children: [/*#__PURE__*/_jsxDEV(ListItem, {\n                  children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: \"Data Sharing\",\n                    secondary: \"Allow anonymized data sharing for research\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 490,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                    children: /*#__PURE__*/_jsxDEV(Switch, {\n                      checked: settings.privacy.data_sharing,\n                      onChange: e => handleSettingChange('privacy', 'data_sharing', e.target.checked)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 495,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 494,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                  children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: \"Analytics\",\n                    secondary: \"Help improve our service with usage analytics\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 503,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                    children: /*#__PURE__*/_jsxDEV(Switch, {\n                      checked: settings.privacy.analytics,\n                      onChange: e => handleSettingChange('privacy', 'analytics', e.target.checked)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 508,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 507,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 502,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 2,\n                mb: 3,\n                children: [/*#__PURE__*/_jsxDEV(Devices, {\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 524,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: \"Session Management\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 525,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                mb: 2,\n                children: \"Manage your active sessions and devices\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                flexDirection: \"column\",\n                gap: 2,\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  startIcon: /*#__PURE__*/_jsxDEV(Devices, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 535,\n                    columnNumber: 32\n                  }, this),\n                  onClick: () => setSessionsDialog(true),\n                  fullWidth: true,\n                  children: \"View Active Sessions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 533,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  color: \"warning\",\n                  onClick: () => revokeAllSessionsMutation.mutate(),\n                  disabled: revokeAllSessionsMutation.isLoading,\n                  fullWidth: true,\n                  children: revokeAllSessionsMutation.isLoading ? 'Revoking...' : 'Logout All Devices'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 542,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 520,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 2,\n                mb: 3,\n                children: [/*#__PURE__*/_jsxDEV(Download, {\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 561,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: \"Data Management\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                mb: 2,\n                children: \"Export your data or manage your account\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 565,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                flexDirection: \"column\",\n                gap: 2,\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  startIcon: /*#__PURE__*/_jsxDEV(Download, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 572,\n                    columnNumber: 32\n                  }, this),\n                  onClick: handleExportData,\n                  disabled: exportingData,\n                  fullWidth: true,\n                  children: exportingData ? 'Exporting...' : 'Export My Data'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 557,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 2,\n                mb: 3,\n                children: [/*#__PURE__*/_jsxDEV(Delete, {\n                  color: \"error\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 589,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"error\",\n                  children: \"Danger Zone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 590,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 588,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Alert, {\n                severity: \"warning\",\n                sx: {\n                  mb: 2\n                },\n                children: \"These actions cannot be undone. Please be careful.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 593,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                color: \"error\",\n                startIcon: /*#__PURE__*/_jsxDEV(Delete, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 600,\n                  columnNumber: 30\n                }, this),\n                onClick: () => setDeleteAccountDialog(true),\n                fullWidth: true,\n                children: \"Delete Account\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 597,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 585,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        mt: 4,\n        display: \"flex\",\n        justifyContent: \"flex-end\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(Save, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 24\n          }, this),\n          onClick: handleSaveSettings,\n          disabled: updateSettingsMutation.isLoading,\n          children: updateSettingsMutation.isLoading ? 'Saving...' : 'Save Settings'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 613,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 612,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: deleteAccountDialog,\n        onClose: () => setDeleteAccountDialog(false),\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          color: \"error\",\n          children: \"Delete Account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 630,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"error\",\n            sx: {\n              mb: 2\n            },\n            children: \"This action will permanently delete your account and all associated data. This cannot be undone.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 632,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            gutterBottom: true,\n            children: \"To confirm, please type \\\"DELETE\\\" in the field below:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 637,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            value: deleteConfirmation,\n            onChange: e => setDeleteConfirmation(e.target.value),\n            placeholder: \"Type DELETE to confirm\",\n            sx: {\n              mt: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 641,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 631,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setDeleteAccountDialog(false),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 650,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"error\",\n            variant: \"contained\",\n            disabled: deleteConfirmation !== 'DELETE' || deleteAccountMutation.isLoading,\n            onClick: handleDeleteAccount,\n            startIcon: deleteAccountMutation.isLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 658,\n              columnNumber: 60\n            }, this) : /*#__PURE__*/_jsxDEV(Delete, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 658,\n              columnNumber: 93\n            }, this),\n            children: deleteAccountMutation.isLoading ? 'Deleting...' : 'Delete Account'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 653,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 649,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 624,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: sessionsDialog,\n        onClose: () => setSessionsDialog(false),\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Active Sessions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 672,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: activeSessions && activeSessions.length > 0 ? /*#__PURE__*/_jsxDEV(List, {\n            children: activeSessions.map(session => /*#__PURE__*/_jsxDEV(ListItem, {\n              divider: true,\n              children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body1\",\n                    children: session.device_name || 'Unknown Device'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 681,\n                    columnNumber: 27\n                  }, this), session.is_current && /*#__PURE__*/_jsxDEV(Chip, {\n                    label: \"Current\",\n                    color: \"primary\",\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 685,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 680,\n                  columnNumber: 25\n                }, this),\n                secondary: /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: [\"IP: \", session.ip_address]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 691,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: [\"Last active: \", new Date(session.last_activity).toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 694,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: [\"Location: \", session.location || 'Unknown']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 697,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 690,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 678,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                children: !session.is_current && /*#__PURE__*/_jsxDEV(Button, {\n                  color: \"error\",\n                  size: \"small\",\n                  onClick: () => revokeSessionMutation.mutate(session.id),\n                  disabled: revokeSessionMutation.isLoading,\n                  children: \"Revoke\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 705,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 703,\n                columnNumber: 21\n              }, this)]\n            }, session.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 677,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 675,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n            children: \"No active sessions found.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 719,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 673,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setSessionsDialog(false),\n            children: \"Close\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 723,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 722,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 666,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(SettingsPage, \"blg/yTLjVElQ+EVAgi7mcUMQDJo=\", false, function () {\n  return [useAuthStore, useQueryClient, useTheme, useQuery, useQuery, useMutation, useMutation, useMutation, useMutation];\n});\n_c = SettingsPage;\nexport default SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "FormControl", "InputLabel", "Select", "MenuItem", "Switch", "<PERSON><PERSON>", "<PERSON><PERSON>", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "CircularProgress", "Chip", "Divider", "Settings", "Notifications", "Security", "Delete", "Save", "Download", "Devices", "Brightness4", "Brightness7", "BrightnessAuto", "<PERSON><PERSON><PERSON>", "toast", "useMutation", "useQueryClient", "useQuery", "useAuthStore", "useTheme", "apiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SettingsPage", "_s", "user", "queryClient", "themeMode", "setThemeMode", "isDarkMode", "settings", "setSettings", "timezone", "language", "theme", "notifications", "email", "push", "security", "portfolio", "marketing", "news", "email_frequency", "push_enabled", "privacy", "profile_visibility", "portfolio_visibility", "data_sharing", "analytics", "deleteAccountDialog", "setDeleteAccountDialog", "deleteConfirmation", "setDeleteConfirmation", "sessionsDialog", "setSessionsDialog", "exportingData", "setExportingData", "data", "userSettings", "isLoading", "settingsLoading", "getUserSettings", "onSuccess", "prev", "onError", "activeSessions", "refetch", "refetchSessions", "getActiveSessions", "enabled", "error", "updateSettingsMutation", "updateUserSettings", "success", "invalidateQueries", "deleteAccountMutation", "confirmation", "deleteAccount", "window", "location", "href", "_error$response", "_error$response$data", "message", "response", "revokeSessionMutation", "sessionId", "revokeSession", "revokeAllSessionsMutation", "revokeAllSessions", "handleSettingChange", "category", "key", "value", "handleThemeChange", "newTheme", "handleSaveSettings", "mutate", "handleExportData", "blob", "exportUserData", "url", "URL", "createObjectURL", "a", "document", "createElement", "download", "Date", "toISOString", "split", "body", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "handleDeleteAccount", "timezones", "label", "languages", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "content", "mb", "variant", "component", "gutterBottom", "color", "container", "spacing", "item", "xs", "md", "display", "alignItems", "gap", "flexDirection", "fullWidth", "onChange", "e", "target", "map", "tz", "lang", "fontSize", "primary", "secondary", "checked", "disabled", "sx", "my", "startIcon", "onClick", "severity", "mt", "justifyContent", "open", "onClose", "max<PERSON><PERSON><PERSON>", "placeholder", "size", "length", "session", "divider", "device_name", "is_current", "ip_address", "last_activity", "toLocaleString", "id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/TrustVault/frontend/src/pages/Settings/SettingsPage.tsx"], "sourcesContent": ["// TrustVault - Settings Page\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Grid,\n  Card,\n  CardContent,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Switch,\n  Button,\n  Alert,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemSecondaryAction,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  CircularProgress,\n  Chip,\n  Divider,\n} from '@mui/material';\nimport {\n  Settings,\n  Notifications,\n  Security,\n  Delete,\n  Save,\n  Download,\n  Devices,\n  Key,\n  Brightness4,\n  Brightness7,\n  BrightnessAuto,\n} from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { toast } from 'react-hot-toast';\nimport { useMutation, useQueryClient, useQuery } from 'react-query';\n\n// Store\nimport { useAuthStore } from '../../store/authStore';\n\n// Contexts\nimport { useTheme } from '../../contexts/ThemeContext';\n\n// Services\nimport apiService from '../../services/api';\n\ninterface UserSettings {\n  timezone: string;\n  language: string;\n  theme: 'light' | 'dark' | 'auto';\n  notifications: {\n    email: boolean;\n    push: boolean;\n    security: boolean;\n    portfolio: boolean;\n    marketing: boolean;\n    news: boolean;\n    email_frequency: 'immediate' | 'daily' | 'weekly' | 'never';\n    push_enabled: boolean;\n  };\n  privacy: {\n    profile_visibility: 'public' | 'private';\n    portfolio_visibility: 'public' | 'private';\n    data_sharing: boolean;\n    analytics: boolean;\n  };\n}\n\nconst SettingsPage: React.FC = () => {\n  const { user } = useAuthStore();\n  const queryClient = useQueryClient();\n  const { themeMode, setThemeMode, isDarkMode } = useTheme();\n\n  const [settings, setSettings] = useState<UserSettings>({\n    timezone: user?.timezone || 'UTC',\n    language: user?.language || 'en',\n    theme: themeMode,\n    notifications: {\n      email: true,\n      push: true,\n      security: true,\n      portfolio: true,\n      marketing: false,\n      news: true,\n      email_frequency: 'daily',\n      push_enabled: true,\n    },\n    privacy: {\n      profile_visibility: 'private',\n      portfolio_visibility: 'private',\n      data_sharing: false,\n      analytics: true,\n    },\n  });\n\n  const [deleteAccountDialog, setDeleteAccountDialog] = useState(false);\n  const [deleteConfirmation, setDeleteConfirmation] = useState('');\n  const [sessionsDialog, setSessionsDialog] = useState(false);\n  const [exportingData, setExportingData] = useState(false);\n\n  // Load user settings\n  const { data: userSettings, isLoading: settingsLoading } = useQuery(\n    'user-settings',\n    apiService.getUserSettings,\n    {\n      onSuccess: (data) => {\n        setSettings(prev => ({ ...prev, ...data }));\n      },\n      onError: () => {\n        // Use default settings if API fails\n      },\n    }\n  );\n\n  // Load active sessions\n  const { data: activeSessions, refetch: refetchSessions } = useQuery(\n    'active-sessions',\n    apiService.getActiveSessions,\n    {\n      enabled: sessionsDialog,\n      onError: () => {\n        toast.error('Failed to load active sessions');\n      },\n    }\n  );\n\n  const updateSettingsMutation = useMutation(\n    (data: Partial<UserSettings>) => apiService.updateUserSettings(data),\n    {\n      onSuccess: () => {\n        toast.success('Settings updated successfully');\n        queryClient.invalidateQueries(['user-settings', 'user-profile']);\n      },\n      onError: () => {\n        toast.error('Failed to update settings');\n      },\n    }\n  );\n\n  const deleteAccountMutation = useMutation(\n    (confirmation: string) => apiService.deleteAccount(confirmation),\n    {\n      onSuccess: () => {\n        toast.success('Account deleted successfully');\n        // Redirect to login or home page\n        window.location.href = '/';\n      },\n      onError: (error: any) => {\n        const message = error.response?.data?.message || 'Failed to delete account';\n        toast.error(message);\n      },\n    }\n  );\n\n  const revokeSessionMutation = useMutation(\n    (sessionId: string) => apiService.revokeSession(sessionId),\n    {\n      onSuccess: () => {\n        toast.success('Session revoked successfully');\n        refetchSessions();\n      },\n      onError: () => {\n        toast.error('Failed to revoke session');\n      },\n    }\n  );\n\n  const revokeAllSessionsMutation = useMutation(\n    () => apiService.revokeAllSessions(),\n    {\n      onSuccess: () => {\n        toast.success('All sessions revoked successfully');\n        refetchSessions();\n      },\n      onError: () => {\n        toast.error('Failed to revoke all sessions');\n      },\n    }\n  );\n\n  const handleSettingChange = (category: keyof UserSettings, key: string, value: any) => {\n    setSettings(prev => ({\n      ...prev,\n      [category]: typeof prev[category] === 'object' && prev[category] !== null\n        ? { ...(prev[category] as Record<string, any>), [key]: value }\n        : value\n    }));\n  };\n\n  const handleThemeChange = (newTheme: 'light' | 'dark' | 'auto') => {\n    setThemeMode(newTheme);\n    handleSettingChange('theme', '', newTheme);\n  };\n\n  const handleSaveSettings = () => {\n    updateSettingsMutation.mutate(settings);\n  };\n\n  const handleExportData = async () => {\n    setExportingData(true);\n    try {\n      const blob = await apiService.exportUserData();\n      const url = window.URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = `trustvault-data-${new Date().toISOString().split('T')[0]}.json`;\n      document.body.appendChild(a);\n      a.click();\n      window.URL.revokeObjectURL(url);\n      document.body.removeChild(a);\n      toast.success('Data exported successfully');\n    } catch (error) {\n      toast.error('Failed to export data');\n    } finally {\n      setExportingData(false);\n    }\n  };\n\n  const handleDeleteAccount = () => {\n    if (deleteConfirmation === 'DELETE') {\n      deleteAccountMutation.mutate(deleteConfirmation);\n    }\n  };\n\n  const timezones = [\n    { value: 'UTC', label: 'UTC' },\n    { value: 'America/New_York', label: 'Eastern Time' },\n    { value: 'America/Chicago', label: 'Central Time' },\n    { value: 'America/Denver', label: 'Mountain Time' },\n    { value: 'America/Los_Angeles', label: 'Pacific Time' },\n    { value: 'Europe/London', label: 'London' },\n    { value: 'Europe/Paris', label: 'Paris' },\n    { value: 'Asia/Tokyo', label: 'Tokyo' },\n  ];\n\n  const languages = [\n    { value: 'en', label: 'English' },\n    { value: 'fr', label: 'Français' },\n    { value: 'es', label: 'Español' },\n    { value: 'de', label: 'Deutsch' },\n  ];\n\n  return (\n    <>\n      <Helmet>\n        <title>Settings - TrustVault</title>\n        <meta name=\"description\" content=\"Manage your account settings and preferences\" />\n      </Helmet>\n\n      <Box>\n        {/* Header */}\n        <Box mb={4}>\n          <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n            Settings\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            Manage your account preferences and security settings\n          </Typography>\n        </Box>\n\n        <Grid container spacing={3}>\n          {/* General Settings */}\n          <Grid item xs={12} md={6}>\n            <Card>\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                  <Settings color=\"primary\" />\n                  <Typography variant=\"h6\">General</Typography>\n                </Box>\n\n                <Box display=\"flex\" flexDirection=\"column\" gap={3}>\n                  <FormControl fullWidth>\n                    <InputLabel>Timezone</InputLabel>\n                    <Select\n                      value={settings.timezone}\n                      onChange={(e) => handleSettingChange('timezone', '', e.target.value)}\n                      label=\"Timezone\"\n                    >\n                      {timezones.map((tz) => (\n                        <MenuItem key={tz.value} value={tz.value}>\n                          {tz.label}\n                        </MenuItem>\n                      ))}\n                    </Select>\n                  </FormControl>\n\n                  <FormControl fullWidth>\n                    <InputLabel>Language</InputLabel>\n                    <Select\n                      value={settings.language}\n                      onChange={(e) => handleSettingChange('language', '', e.target.value)}\n                      label=\"Language\"\n                    >\n                      {languages.map((lang) => (\n                        <MenuItem key={lang.value} value={lang.value}>\n                          {lang.label}\n                        </MenuItem>\n                      ))}\n                    </Select>\n                  </FormControl>\n\n                  <FormControl fullWidth>\n                    <InputLabel>Theme</InputLabel>\n                    <Select\n                      value={themeMode}\n                      onChange={(e) => handleThemeChange(e.target.value as 'light' | 'dark' | 'auto')}\n                      label=\"Theme\"\n                    >\n                      <MenuItem value=\"light\">\n                        <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                          <Brightness7 fontSize=\"small\" />\n                          Light\n                        </Box>\n                      </MenuItem>\n                      <MenuItem value=\"dark\">\n                        <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                          <Brightness4 fontSize=\"small\" />\n                          Dark\n                        </Box>\n                      </MenuItem>\n                      <MenuItem value=\"auto\">\n                        <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                          <BrightnessAuto fontSize=\"small\" />\n                          Auto (System)\n                        </Box>\n                      </MenuItem>\n                    </Select>\n                  </FormControl>\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n\n          {/* Notification Settings */}\n          <Grid item xs={12} md={6}>\n            <Card>\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                  <Notifications color=\"primary\" />\n                  <Typography variant=\"h6\">Notifications</Typography>\n                </Box>\n\n                <Box display=\"flex\" flexDirection=\"column\" gap={2} mb={3}>\n                  <FormControl fullWidth>\n                    <InputLabel>Email Frequency</InputLabel>\n                    <Select\n                      value={settings.notifications.email_frequency}\n                      onChange={(e) => handleSettingChange('notifications', 'email_frequency', e.target.value)}\n                      label=\"Email Frequency\"\n                    >\n                      <MenuItem value=\"immediate\">Immediate</MenuItem>\n                      <MenuItem value=\"daily\">Daily Digest</MenuItem>\n                      <MenuItem value=\"weekly\">Weekly Summary</MenuItem>\n                      <MenuItem value=\"never\">Never</MenuItem>\n                    </Select>\n                  </FormControl>\n                </Box>\n\n                <List>\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Email Notifications\"\n                      secondary=\"Receive updates via email\"\n                    />\n                    <ListItemSecondaryAction>\n                      <Switch\n                        checked={settings.notifications.email}\n                        onChange={(e) => handleSettingChange('notifications', 'email', e.target.checked)}\n                      />\n                    </ListItemSecondaryAction>\n                  </ListItem>\n\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Push Notifications\"\n                      secondary=\"Browser push notifications\"\n                    />\n                    <ListItemSecondaryAction>\n                      <Switch\n                        checked={settings.notifications.push}\n                        onChange={(e) => handleSettingChange('notifications', 'push', e.target.checked)}\n                      />\n                    </ListItemSecondaryAction>\n                  </ListItem>\n\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Security Alerts\"\n                      secondary=\"Important security notifications (always enabled)\"\n                    />\n                    <ListItemSecondaryAction>\n                      <Switch\n                        checked={true}\n                        disabled={true}\n                      />\n                    </ListItemSecondaryAction>\n                  </ListItem>\n\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Portfolio Updates\"\n                      secondary=\"Portfolio performance notifications\"\n                    />\n                    <ListItemSecondaryAction>\n                      <Switch\n                        checked={settings.notifications.portfolio}\n                        onChange={(e) => handleSettingChange('notifications', 'portfolio', e.target.checked)}\n                      />\n                    </ListItemSecondaryAction>\n                  </ListItem>\n\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Marketing Communications\"\n                      secondary=\"Product updates and promotional content\"\n                    />\n                    <ListItemSecondaryAction>\n                      <Switch\n                        checked={settings.notifications.marketing}\n                        onChange={(e) => handleSettingChange('notifications', 'marketing', e.target.checked)}\n                      />\n                    </ListItemSecondaryAction>\n                  </ListItem>\n\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Market News\"\n                      secondary=\"Financial market news and insights\"\n                    />\n                    <ListItemSecondaryAction>\n                      <Switch\n                        checked={settings.notifications.news}\n                        onChange={(e) => handleSettingChange('notifications', 'news', e.target.checked)}\n                      />\n                    </ListItemSecondaryAction>\n                  </ListItem>\n                </List>\n              </CardContent>\n            </Card>\n          </Grid>\n\n          {/* Privacy Settings */}\n          <Grid item xs={12} md={6}>\n            <Card>\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                  <Security color=\"primary\" />\n                  <Typography variant=\"h6\">Privacy</Typography>\n                </Box>\n\n                <Box display=\"flex\" flexDirection=\"column\" gap={3}>\n                  <FormControl fullWidth>\n                    <InputLabel>Profile Visibility</InputLabel>\n                    <Select\n                      value={settings.privacy.profile_visibility}\n                      onChange={(e) => handleSettingChange('privacy', 'profile_visibility', e.target.value)}\n                      label=\"Profile Visibility\"\n                    >\n                      <MenuItem value=\"public\">Public</MenuItem>\n                      <MenuItem value=\"private\">Private</MenuItem>\n                    </Select>\n                  </FormControl>\n\n                  <FormControl fullWidth>\n                    <InputLabel>Portfolio Visibility</InputLabel>\n                    <Select\n                      value={settings.privacy.portfolio_visibility}\n                      onChange={(e) => handleSettingChange('privacy', 'portfolio_visibility', e.target.value)}\n                      label=\"Portfolio Visibility\"\n                    >\n                      <MenuItem value=\"public\">Public</MenuItem>\n                      <MenuItem value=\"private\">Private</MenuItem>\n                    </Select>\n                  </FormControl>\n                </Box>\n\n                <Divider sx={{ my: 2 }} />\n\n                <List>\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Data Sharing\"\n                      secondary=\"Allow anonymized data sharing for research\"\n                    />\n                    <ListItemSecondaryAction>\n                      <Switch\n                        checked={settings.privacy.data_sharing}\n                        onChange={(e) => handleSettingChange('privacy', 'data_sharing', e.target.checked)}\n                      />\n                    </ListItemSecondaryAction>\n                  </ListItem>\n\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Analytics\"\n                      secondary=\"Help improve our service with usage analytics\"\n                    />\n                    <ListItemSecondaryAction>\n                      <Switch\n                        checked={settings.privacy.analytics}\n                        onChange={(e) => handleSettingChange('privacy', 'analytics', e.target.checked)}\n                      />\n                    </ListItemSecondaryAction>\n                  </ListItem>\n                </List>\n              </CardContent>\n            </Card>\n          </Grid>\n\n          {/* Session Management */}\n          <Grid item xs={12} md={6}>\n            <Card>\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                  <Devices color=\"primary\" />\n                  <Typography variant=\"h6\">Session Management</Typography>\n                </Box>\n\n                <Typography variant=\"body2\" color=\"text.secondary\" mb={2}>\n                  Manage your active sessions and devices\n                </Typography>\n\n                <Box display=\"flex\" flexDirection=\"column\" gap={2}>\n                  <Button\n                    variant=\"outlined\"\n                    startIcon={<Devices />}\n                    onClick={() => setSessionsDialog(true)}\n                    fullWidth\n                  >\n                    View Active Sessions\n                  </Button>\n\n                  <Button\n                    variant=\"outlined\"\n                    color=\"warning\"\n                    onClick={() => revokeAllSessionsMutation.mutate()}\n                    disabled={revokeAllSessionsMutation.isLoading}\n                    fullWidth\n                  >\n                    {revokeAllSessionsMutation.isLoading ? 'Revoking...' : 'Logout All Devices'}\n                  </Button>\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n\n          {/* Data Management */}\n          <Grid item xs={12} md={6}>\n            <Card>\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                  <Download color=\"primary\" />\n                  <Typography variant=\"h6\">Data Management</Typography>\n                </Box>\n\n                <Typography variant=\"body2\" color=\"text.secondary\" mb={2}>\n                  Export your data or manage your account\n                </Typography>\n\n                <Box display=\"flex\" flexDirection=\"column\" gap={2}>\n                  <Button\n                    variant=\"outlined\"\n                    startIcon={<Download />}\n                    onClick={handleExportData}\n                    disabled={exportingData}\n                    fullWidth\n                  >\n                    {exportingData ? 'Exporting...' : 'Export My Data'}\n                  </Button>\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n\n          {/* Danger Zone */}\n          <Grid item xs={12} md={6}>\n            <Card>\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                  <Delete color=\"error\" />\n                  <Typography variant=\"h6\" color=\"error\">Danger Zone</Typography>\n                </Box>\n\n                <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                  These actions cannot be undone. Please be careful.\n                </Alert>\n\n                <Button\n                  variant=\"outlined\"\n                  color=\"error\"\n                  startIcon={<Delete />}\n                  onClick={() => setDeleteAccountDialog(true)}\n                  fullWidth\n                >\n                  Delete Account\n                </Button>\n              </CardContent>\n            </Card>\n          </Grid>\n        </Grid>\n\n        {/* Save Button */}\n        <Box mt={4} display=\"flex\" justifyContent=\"flex-end\">\n          <Button\n            variant=\"contained\"\n            startIcon={<Save />}\n            onClick={handleSaveSettings}\n            disabled={updateSettingsMutation.isLoading}\n          >\n            {updateSettingsMutation.isLoading ? 'Saving...' : 'Save Settings'}\n          </Button>\n        </Box>\n\n        {/* Delete Account Dialog */}\n        <Dialog\n          open={deleteAccountDialog}\n          onClose={() => setDeleteAccountDialog(false)}\n          maxWidth=\"sm\"\n          fullWidth\n        >\n          <DialogTitle color=\"error\">Delete Account</DialogTitle>\n          <DialogContent>\n            <Alert severity=\"error\" sx={{ mb: 2 }}>\n              This action will permanently delete your account and all associated data.\n              This cannot be undone.\n            </Alert>\n            \n            <Typography variant=\"body2\" gutterBottom>\n              To confirm, please type \"DELETE\" in the field below:\n            </Typography>\n            \n            <TextField\n              fullWidth\n              value={deleteConfirmation}\n              onChange={(e) => setDeleteConfirmation(e.target.value)}\n              placeholder=\"Type DELETE to confirm\"\n              sx={{ mt: 2 }}\n            />\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={() => setDeleteAccountDialog(false)}>\n              Cancel\n            </Button>\n            <Button\n              color=\"error\"\n              variant=\"contained\"\n              disabled={deleteConfirmation !== 'DELETE' || deleteAccountMutation.isLoading}\n              onClick={handleDeleteAccount}\n              startIcon={deleteAccountMutation.isLoading ? <CircularProgress size={16} /> : <Delete />}\n            >\n              {deleteAccountMutation.isLoading ? 'Deleting...' : 'Delete Account'}\n            </Button>\n          </DialogActions>\n        </Dialog>\n\n        {/* Active Sessions Dialog */}\n        <Dialog\n          open={sessionsDialog}\n          onClose={() => setSessionsDialog(false)}\n          maxWidth=\"md\"\n          fullWidth\n        >\n          <DialogTitle>Active Sessions</DialogTitle>\n          <DialogContent>\n            {activeSessions && activeSessions.length > 0 ? (\n              <List>\n                {activeSessions.map((session: any) => (\n                  <ListItem key={session.id} divider>\n                    <ListItemText\n                      primary={\n                        <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                          <Typography variant=\"body1\">\n                            {session.device_name || 'Unknown Device'}\n                          </Typography>\n                          {session.is_current && (\n                            <Chip label=\"Current\" color=\"primary\" size=\"small\" />\n                          )}\n                        </Box>\n                      }\n                      secondary={\n                        <Box>\n                          <Typography variant=\"body2\" color=\"text.secondary\">\n                            IP: {session.ip_address}\n                          </Typography>\n                          <Typography variant=\"body2\" color=\"text.secondary\">\n                            Last active: {new Date(session.last_activity).toLocaleString()}\n                          </Typography>\n                          <Typography variant=\"body2\" color=\"text.secondary\">\n                            Location: {session.location || 'Unknown'}\n                          </Typography>\n                        </Box>\n                      }\n                    />\n                    <ListItemSecondaryAction>\n                      {!session.is_current && (\n                        <Button\n                          color=\"error\"\n                          size=\"small\"\n                          onClick={() => revokeSessionMutation.mutate(session.id)}\n                          disabled={revokeSessionMutation.isLoading}\n                        >\n                          Revoke\n                        </Button>\n                      )}\n                    </ListItemSecondaryAction>\n                  </ListItem>\n                ))}\n              </List>\n            ) : (\n              <Typography>No active sessions found.</Typography>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={() => setSessionsDialog(false)}>\n              Close\n            </Button>\n          </DialogActions>\n        </Dialog>\n      </Box>\n    </>\n  );\n};\n\nexport default SettingsPage;\n"], "mappings": ";;AAAA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,QAAmB,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,QACF,eAAe;AACtB,SACEC,QAAQ,EACRC,aAAa,EACbC,QAAQ,EACRC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,OAAO,EAEPC,WAAW,EACXC,WAAW,EACXC,cAAc,QACT,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,KAAK,QAAQ,iBAAiB;AACvC,SAASC,WAAW,EAAEC,cAAc,EAAEC,QAAQ,QAAQ,aAAa;;AAEnE;AACA,SAASC,YAAY,QAAQ,uBAAuB;;AAEpD;AACA,SAASC,QAAQ,QAAQ,6BAA6B;;AAEtD;AACA,OAAOC,UAAU,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAwB5C,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM;IAAEC;EAAK,CAAC,GAAGT,YAAY,CAAC,CAAC;EAC/B,MAAMU,WAAW,GAAGZ,cAAc,CAAC,CAAC;EACpC,MAAM;IAAEa,SAAS;IAAEC,YAAY;IAAEC;EAAW,CAAC,GAAGZ,QAAQ,CAAC,CAAC;EAE1D,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGvD,QAAQ,CAAe;IACrDwD,QAAQ,EAAE,CAAAP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,QAAQ,KAAI,KAAK;IACjCC,QAAQ,EAAE,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,QAAQ,KAAI,IAAI;IAChCC,KAAK,EAAEP,SAAS;IAChBQ,aAAa,EAAE;MACbC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,KAAK;MAChBC,IAAI,EAAE,IAAI;MACVC,eAAe,EAAE,OAAO;MACxBC,YAAY,EAAE;IAChB,CAAC;IACDC,OAAO,EAAE;MACPC,kBAAkB,EAAE,SAAS;MAC7BC,oBAAoB,EAAE,SAAS;MAC/BC,YAAY,EAAE,KAAK;MACnBC,SAAS,EAAE;IACb;EACF,CAAC,CAAC;EAEF,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC2E,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAAC6E,cAAc,EAAEC,iBAAiB,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC+E,aAAa,EAAEC,gBAAgB,CAAC,GAAGhF,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM;IAAEiF,IAAI,EAAEC,YAAY;IAAEC,SAAS,EAAEC;EAAgB,CAAC,GAAG7C,QAAQ,CACjE,eAAe,EACfG,UAAU,CAAC2C,eAAe,EAC1B;IACEC,SAAS,EAAGL,IAAI,IAAK;MACnB1B,WAAW,CAACgC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,GAAGN;MAAK,CAAC,CAAC,CAAC;IAC7C,CAAC;IACDO,OAAO,EAAEA,CAAA,KAAM;MACb;IAAA;EAEJ,CACF,CAAC;;EAED;EACA,MAAM;IAAEP,IAAI,EAAEQ,cAAc;IAAEC,OAAO,EAAEC;EAAgB,CAAC,GAAGpD,QAAQ,CACjE,iBAAiB,EACjBG,UAAU,CAACkD,iBAAiB,EAC5B;IACEC,OAAO,EAAEhB,cAAc;IACvBW,OAAO,EAAEA,CAAA,KAAM;MACbpD,KAAK,CAAC0D,KAAK,CAAC,gCAAgC,CAAC;IAC/C;EACF,CACF,CAAC;EAED,MAAMC,sBAAsB,GAAG1D,WAAW,CACvC4C,IAA2B,IAAKvC,UAAU,CAACsD,kBAAkB,CAACf,IAAI,CAAC,EACpE;IACEK,SAAS,EAAEA,CAAA,KAAM;MACflD,KAAK,CAAC6D,OAAO,CAAC,+BAA+B,CAAC;MAC9C/C,WAAW,CAACgD,iBAAiB,CAAC,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC;IAClE,CAAC;IACDV,OAAO,EAAEA,CAAA,KAAM;MACbpD,KAAK,CAAC0D,KAAK,CAAC,2BAA2B,CAAC;IAC1C;EACF,CACF,CAAC;EAED,MAAMK,qBAAqB,GAAG9D,WAAW,CACtC+D,YAAoB,IAAK1D,UAAU,CAAC2D,aAAa,CAACD,YAAY,CAAC,EAChE;IACEd,SAAS,EAAEA,CAAA,KAAM;MACflD,KAAK,CAAC6D,OAAO,CAAC,8BAA8B,CAAC;MAC7C;MACAK,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;IAC5B,CAAC;IACDhB,OAAO,EAAGM,KAAU,IAAK;MAAA,IAAAW,eAAA,EAAAC,oBAAA;MACvB,MAAMC,OAAO,GAAG,EAAAF,eAAA,GAAAX,KAAK,CAACc,QAAQ,cAAAH,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBxB,IAAI,cAAAyB,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,0BAA0B;MAC3EvE,KAAK,CAAC0D,KAAK,CAACa,OAAO,CAAC;IACtB;EACF,CACF,CAAC;EAED,MAAME,qBAAqB,GAAGxE,WAAW,CACtCyE,SAAiB,IAAKpE,UAAU,CAACqE,aAAa,CAACD,SAAS,CAAC,EAC1D;IACExB,SAAS,EAAEA,CAAA,KAAM;MACflD,KAAK,CAAC6D,OAAO,CAAC,8BAA8B,CAAC;MAC7CN,eAAe,CAAC,CAAC;IACnB,CAAC;IACDH,OAAO,EAAEA,CAAA,KAAM;MACbpD,KAAK,CAAC0D,KAAK,CAAC,0BAA0B,CAAC;IACzC;EACF,CACF,CAAC;EAED,MAAMkB,yBAAyB,GAAG3E,WAAW,CAC3C,MAAMK,UAAU,CAACuE,iBAAiB,CAAC,CAAC,EACpC;IACE3B,SAAS,EAAEA,CAAA,KAAM;MACflD,KAAK,CAAC6D,OAAO,CAAC,mCAAmC,CAAC;MAClDN,eAAe,CAAC,CAAC;IACnB,CAAC;IACDH,OAAO,EAAEA,CAAA,KAAM;MACbpD,KAAK,CAAC0D,KAAK,CAAC,+BAA+B,CAAC;IAC9C;EACF,CACF,CAAC;EAED,MAAMoB,mBAAmB,GAAGA,CAACC,QAA4B,EAAEC,GAAW,EAAEC,KAAU,KAAK;IACrF9D,WAAW,CAACgC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAAC4B,QAAQ,GAAG,OAAO5B,IAAI,CAAC4B,QAAQ,CAAC,KAAK,QAAQ,IAAI5B,IAAI,CAAC4B,QAAQ,CAAC,KAAK,IAAI,GACrE;QAAE,GAAI5B,IAAI,CAAC4B,QAAQ,CAAyB;QAAE,CAACC,GAAG,GAAGC;MAAM,CAAC,GAC5DA;IACN,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,iBAAiB,GAAIC,QAAmC,IAAK;IACjEnE,YAAY,CAACmE,QAAQ,CAAC;IACtBL,mBAAmB,CAAC,OAAO,EAAE,EAAE,EAAEK,QAAQ,CAAC;EAC5C,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BzB,sBAAsB,CAAC0B,MAAM,CAACnE,QAAQ,CAAC;EACzC,CAAC;EAED,MAAMoE,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC1C,gBAAgB,CAAC,IAAI,CAAC;IACtB,IAAI;MACF,MAAM2C,IAAI,GAAG,MAAMjF,UAAU,CAACkF,cAAc,CAAC,CAAC;MAC9C,MAAMC,GAAG,GAAGvB,MAAM,CAACwB,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;MAC5C,MAAMK,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACrCF,CAAC,CAACxB,IAAI,GAAGqB,GAAG;MACZG,CAAC,CAACG,QAAQ,GAAG,mBAAmB,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO;MAC7EL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,CAAC,CAAC;MAC5BA,CAAC,CAACS,KAAK,CAAC,CAAC;MACTnC,MAAM,CAACwB,GAAG,CAACY,eAAe,CAACb,GAAG,CAAC;MAC/BI,QAAQ,CAACM,IAAI,CAACI,WAAW,CAACX,CAAC,CAAC;MAC5B5F,KAAK,CAAC6D,OAAO,CAAC,4BAA4B,CAAC;IAC7C,CAAC,CAAC,OAAOH,KAAK,EAAE;MACd1D,KAAK,CAAC0D,KAAK,CAAC,uBAAuB,CAAC;IACtC,CAAC,SAAS;MACRd,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAM4D,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIjE,kBAAkB,KAAK,QAAQ,EAAE;MACnCwB,qBAAqB,CAACsB,MAAM,CAAC9C,kBAAkB,CAAC;IAClD;EACF,CAAC;EAED,MAAMkE,SAAS,GAAG,CAChB;IAAExB,KAAK,EAAE,KAAK;IAAEyB,KAAK,EAAE;EAAM,CAAC,EAC9B;IAAEzB,KAAK,EAAE,kBAAkB;IAAEyB,KAAK,EAAE;EAAe,CAAC,EACpD;IAAEzB,KAAK,EAAE,iBAAiB;IAAEyB,KAAK,EAAE;EAAe,CAAC,EACnD;IAAEzB,KAAK,EAAE,gBAAgB;IAAEyB,KAAK,EAAE;EAAgB,CAAC,EACnD;IAAEzB,KAAK,EAAE,qBAAqB;IAAEyB,KAAK,EAAE;EAAe,CAAC,EACvD;IAAEzB,KAAK,EAAE,eAAe;IAAEyB,KAAK,EAAE;EAAS,CAAC,EAC3C;IAAEzB,KAAK,EAAE,cAAc;IAAEyB,KAAK,EAAE;EAAQ,CAAC,EACzC;IAAEzB,KAAK,EAAE,YAAY;IAAEyB,KAAK,EAAE;EAAQ,CAAC,CACxC;EAED,MAAMC,SAAS,GAAG,CAChB;IAAE1B,KAAK,EAAE,IAAI;IAAEyB,KAAK,EAAE;EAAU,CAAC,EACjC;IAAEzB,KAAK,EAAE,IAAI;IAAEyB,KAAK,EAAE;EAAW,CAAC,EAClC;IAAEzB,KAAK,EAAE,IAAI;IAAEyB,KAAK,EAAE;EAAU,CAAC,EACjC;IAAEzB,KAAK,EAAE,IAAI;IAAEyB,KAAK,EAAE;EAAU,CAAC,CAClC;EAED,oBACElG,OAAA,CAAAE,SAAA;IAAAkG,QAAA,gBACEpG,OAAA,CAACT,MAAM;MAAA6G,QAAA,gBACLpG,OAAA;QAAAoG,QAAA,EAAO;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACpCxG,OAAA;QAAMyG,IAAI,EAAC,aAAa;QAACC,OAAO,EAAC;MAA8C;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5E,CAAC,eAETxG,OAAA,CAAC3C,GAAG;MAAA+I,QAAA,gBAEFpG,OAAA,CAAC3C,GAAG;QAACsJ,EAAE,EAAE,CAAE;QAAAP,QAAA,gBACTpG,OAAA,CAAC1C,UAAU;UAACsJ,OAAO,EAAC,IAAI;UAACC,SAAS,EAAC,IAAI;UAACC,YAAY;UAAAV,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbxG,OAAA,CAAC1C,UAAU;UAACsJ,OAAO,EAAC,OAAO;UAACG,KAAK,EAAC,gBAAgB;UAAAX,QAAA,EAAC;QAEnD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAENxG,OAAA,CAACzC,IAAI;QAACyJ,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAb,QAAA,gBAEzBpG,OAAA,CAACzC,IAAI;UAAC2J,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eACvBpG,OAAA,CAACxC,IAAI;YAAA4I,QAAA,eACHpG,OAAA,CAACvC,WAAW;cAAA2I,QAAA,gBACVpG,OAAA,CAAC3C,GAAG;gBAACgK,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,GAAG,EAAE,CAAE;gBAACZ,EAAE,EAAE,CAAE;gBAAAP,QAAA,gBACpDpG,OAAA,CAACnB,QAAQ;kBAACkI,KAAK,EAAC;gBAAS;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5BxG,OAAA,CAAC1C,UAAU;kBAACsJ,OAAO,EAAC,IAAI;kBAAAR,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eAENxG,OAAA,CAAC3C,GAAG;gBAACgK,OAAO,EAAC,MAAM;gBAACG,aAAa,EAAC,QAAQ;gBAACD,GAAG,EAAE,CAAE;gBAAAnB,QAAA,gBAChDpG,OAAA,CAACtC,WAAW;kBAAC+J,SAAS;kBAAArB,QAAA,gBACpBpG,OAAA,CAACrC,UAAU;oBAAAyI,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACjCxG,OAAA,CAACpC,MAAM;oBACL6G,KAAK,EAAE/D,QAAQ,CAACE,QAAS;oBACzB8G,QAAQ,EAAGC,CAAC,IAAKrD,mBAAmB,CAAC,UAAU,EAAE,EAAE,EAAEqD,CAAC,CAACC,MAAM,CAACnD,KAAK,CAAE;oBACrEyB,KAAK,EAAC,UAAU;oBAAAE,QAAA,EAEfH,SAAS,CAAC4B,GAAG,CAAEC,EAAE,iBAChB9H,OAAA,CAACnC,QAAQ;sBAAgB4G,KAAK,EAAEqD,EAAE,CAACrD,KAAM;sBAAA2B,QAAA,EACtC0B,EAAE,CAAC5B;oBAAK,GADI4B,EAAE,CAACrD,KAAK;sBAAA4B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEb,CACX;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAEdxG,OAAA,CAACtC,WAAW;kBAAC+J,SAAS;kBAAArB,QAAA,gBACpBpG,OAAA,CAACrC,UAAU;oBAAAyI,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACjCxG,OAAA,CAACpC,MAAM;oBACL6G,KAAK,EAAE/D,QAAQ,CAACG,QAAS;oBACzB6G,QAAQ,EAAGC,CAAC,IAAKrD,mBAAmB,CAAC,UAAU,EAAE,EAAE,EAAEqD,CAAC,CAACC,MAAM,CAACnD,KAAK,CAAE;oBACrEyB,KAAK,EAAC,UAAU;oBAAAE,QAAA,EAEfD,SAAS,CAAC0B,GAAG,CAAEE,IAAI,iBAClB/H,OAAA,CAACnC,QAAQ;sBAAkB4G,KAAK,EAAEsD,IAAI,CAACtD,KAAM;sBAAA2B,QAAA,EAC1C2B,IAAI,CAAC7B;oBAAK,GADE6B,IAAI,CAACtD,KAAK;sBAAA4B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEf,CACX;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAEdxG,OAAA,CAACtC,WAAW;kBAAC+J,SAAS;kBAAArB,QAAA,gBACpBpG,OAAA,CAACrC,UAAU;oBAAAyI,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC9BxG,OAAA,CAACpC,MAAM;oBACL6G,KAAK,EAAElE,SAAU;oBACjBmH,QAAQ,EAAGC,CAAC,IAAKjD,iBAAiB,CAACiD,CAAC,CAACC,MAAM,CAACnD,KAAkC,CAAE;oBAChFyB,KAAK,EAAC,OAAO;oBAAAE,QAAA,gBAEbpG,OAAA,CAACnC,QAAQ;sBAAC4G,KAAK,EAAC,OAAO;sBAAA2B,QAAA,eACrBpG,OAAA,CAAC3C,GAAG;wBAACgK,OAAO,EAAC,MAAM;wBAACC,UAAU,EAAC,QAAQ;wBAACC,GAAG,EAAE,CAAE;wBAAAnB,QAAA,gBAC7CpG,OAAA,CAACX,WAAW;0BAAC2I,QAAQ,EAAC;wBAAO;0BAAA3B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,SAElC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eACXxG,OAAA,CAACnC,QAAQ;sBAAC4G,KAAK,EAAC,MAAM;sBAAA2B,QAAA,eACpBpG,OAAA,CAAC3C,GAAG;wBAACgK,OAAO,EAAC,MAAM;wBAACC,UAAU,EAAC,QAAQ;wBAACC,GAAG,EAAE,CAAE;wBAAAnB,QAAA,gBAC7CpG,OAAA,CAACZ,WAAW;0BAAC4I,QAAQ,EAAC;wBAAO;0BAAA3B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,QAElC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eACXxG,OAAA,CAACnC,QAAQ;sBAAC4G,KAAK,EAAC,MAAM;sBAAA2B,QAAA,eACpBpG,OAAA,CAAC3C,GAAG;wBAACgK,OAAO,EAAC,MAAM;wBAACC,UAAU,EAAC,QAAQ;wBAACC,GAAG,EAAE,CAAE;wBAAAnB,QAAA,gBAC7CpG,OAAA,CAACV,cAAc;0BAAC0I,QAAQ,EAAC;wBAAO;0BAAA3B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,iBAErC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGPxG,OAAA,CAACzC,IAAI;UAAC2J,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eACvBpG,OAAA,CAACxC,IAAI;YAAA4I,QAAA,eACHpG,OAAA,CAACvC,WAAW;cAAA2I,QAAA,gBACVpG,OAAA,CAAC3C,GAAG;gBAACgK,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,GAAG,EAAE,CAAE;gBAACZ,EAAE,EAAE,CAAE;gBAAAP,QAAA,gBACpDpG,OAAA,CAAClB,aAAa;kBAACiI,KAAK,EAAC;gBAAS;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjCxG,OAAA,CAAC1C,UAAU;kBAACsJ,OAAO,EAAC,IAAI;kBAAAR,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eAENxG,OAAA,CAAC3C,GAAG;gBAACgK,OAAO,EAAC,MAAM;gBAACG,aAAa,EAAC,QAAQ;gBAACD,GAAG,EAAE,CAAE;gBAACZ,EAAE,EAAE,CAAE;gBAAAP,QAAA,eACvDpG,OAAA,CAACtC,WAAW;kBAAC+J,SAAS;kBAAArB,QAAA,gBACpBpG,OAAA,CAACrC,UAAU;oBAAAyI,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACxCxG,OAAA,CAACpC,MAAM;oBACL6G,KAAK,EAAE/D,QAAQ,CAACK,aAAa,CAACO,eAAgB;oBAC9CoG,QAAQ,EAAGC,CAAC,IAAKrD,mBAAmB,CAAC,eAAe,EAAE,iBAAiB,EAAEqD,CAAC,CAACC,MAAM,CAACnD,KAAK,CAAE;oBACzFyB,KAAK,EAAC,iBAAiB;oBAAAE,QAAA,gBAEvBpG,OAAA,CAACnC,QAAQ;sBAAC4G,KAAK,EAAC,WAAW;sBAAA2B,QAAA,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eAChDxG,OAAA,CAACnC,QAAQ;sBAAC4G,KAAK,EAAC,OAAO;sBAAA2B,QAAA,EAAC;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eAC/CxG,OAAA,CAACnC,QAAQ;sBAAC4G,KAAK,EAAC,QAAQ;sBAAA2B,QAAA,EAAC;oBAAc;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eAClDxG,OAAA,CAACnC,QAAQ;sBAAC4G,KAAK,EAAC,OAAO;sBAAA2B,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eAENxG,OAAA,CAAC/B,IAAI;gBAAAmI,QAAA,gBACHpG,OAAA,CAAC9B,QAAQ;kBAAAkI,QAAA,gBACPpG,OAAA,CAAC7B,YAAY;oBACX8J,OAAO,EAAC,qBAAqB;oBAC7BC,SAAS,EAAC;kBAA2B;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,eACFxG,OAAA,CAAC5B,uBAAuB;oBAAAgI,QAAA,eACtBpG,OAAA,CAAClC,MAAM;sBACLqK,OAAO,EAAEzH,QAAQ,CAACK,aAAa,CAACC,KAAM;sBACtC0G,QAAQ,EAAGC,CAAC,IAAKrD,mBAAmB,CAAC,eAAe,EAAE,OAAO,EAAEqD,CAAC,CAACC,MAAM,CAACO,OAAO;oBAAE;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACqB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eAEXxG,OAAA,CAAC9B,QAAQ;kBAAAkI,QAAA,gBACPpG,OAAA,CAAC7B,YAAY;oBACX8J,OAAO,EAAC,oBAAoB;oBAC5BC,SAAS,EAAC;kBAA4B;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC,eACFxG,OAAA,CAAC5B,uBAAuB;oBAAAgI,QAAA,eACtBpG,OAAA,CAAClC,MAAM;sBACLqK,OAAO,EAAEzH,QAAQ,CAACK,aAAa,CAACE,IAAK;sBACrCyG,QAAQ,EAAGC,CAAC,IAAKrD,mBAAmB,CAAC,eAAe,EAAE,MAAM,EAAEqD,CAAC,CAACC,MAAM,CAACO,OAAO;oBAAE;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACqB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eAEXxG,OAAA,CAAC9B,QAAQ;kBAAAkI,QAAA,gBACPpG,OAAA,CAAC7B,YAAY;oBACX8J,OAAO,EAAC,iBAAiB;oBACzBC,SAAS,EAAC;kBAAmD;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC,eACFxG,OAAA,CAAC5B,uBAAuB;oBAAAgI,QAAA,eACtBpG,OAAA,CAAClC,MAAM;sBACLqK,OAAO,EAAE,IAAK;sBACdC,QAAQ,EAAE;oBAAK;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACqB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eAEXxG,OAAA,CAAC9B,QAAQ;kBAAAkI,QAAA,gBACPpG,OAAA,CAAC7B,YAAY;oBACX8J,OAAO,EAAC,mBAAmB;oBAC3BC,SAAS,EAAC;kBAAqC;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC,eACFxG,OAAA,CAAC5B,uBAAuB;oBAAAgI,QAAA,eACtBpG,OAAA,CAAClC,MAAM;sBACLqK,OAAO,EAAEzH,QAAQ,CAACK,aAAa,CAACI,SAAU;sBAC1CuG,QAAQ,EAAGC,CAAC,IAAKrD,mBAAmB,CAAC,eAAe,EAAE,WAAW,EAAEqD,CAAC,CAACC,MAAM,CAACO,OAAO;oBAAE;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACqB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eAEXxG,OAAA,CAAC9B,QAAQ;kBAAAkI,QAAA,gBACPpG,OAAA,CAAC7B,YAAY;oBACX8J,OAAO,EAAC,0BAA0B;oBAClCC,SAAS,EAAC;kBAAyC;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC,eACFxG,OAAA,CAAC5B,uBAAuB;oBAAAgI,QAAA,eACtBpG,OAAA,CAAClC,MAAM;sBACLqK,OAAO,EAAEzH,QAAQ,CAACK,aAAa,CAACK,SAAU;sBAC1CsG,QAAQ,EAAGC,CAAC,IAAKrD,mBAAmB,CAAC,eAAe,EAAE,WAAW,EAAEqD,CAAC,CAACC,MAAM,CAACO,OAAO;oBAAE;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACqB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eAEXxG,OAAA,CAAC9B,QAAQ;kBAAAkI,QAAA,gBACPpG,OAAA,CAAC7B,YAAY;oBACX8J,OAAO,EAAC,aAAa;oBACrBC,SAAS,EAAC;kBAAoC;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC,eACFxG,OAAA,CAAC5B,uBAAuB;oBAAAgI,QAAA,eACtBpG,OAAA,CAAClC,MAAM;sBACLqK,OAAO,EAAEzH,QAAQ,CAACK,aAAa,CAACM,IAAK;sBACrCqG,QAAQ,EAAGC,CAAC,IAAKrD,mBAAmB,CAAC,eAAe,EAAE,MAAM,EAAEqD,CAAC,CAACC,MAAM,CAACO,OAAO;oBAAE;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACqB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGPxG,OAAA,CAACzC,IAAI;UAAC2J,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eACvBpG,OAAA,CAACxC,IAAI;YAAA4I,QAAA,eACHpG,OAAA,CAACvC,WAAW;cAAA2I,QAAA,gBACVpG,OAAA,CAAC3C,GAAG;gBAACgK,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,GAAG,EAAE,CAAE;gBAACZ,EAAE,EAAE,CAAE;gBAAAP,QAAA,gBACpDpG,OAAA,CAACjB,QAAQ;kBAACgI,KAAK,EAAC;gBAAS;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5BxG,OAAA,CAAC1C,UAAU;kBAACsJ,OAAO,EAAC,IAAI;kBAAAR,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eAENxG,OAAA,CAAC3C,GAAG;gBAACgK,OAAO,EAAC,MAAM;gBAACG,aAAa,EAAC,QAAQ;gBAACD,GAAG,EAAE,CAAE;gBAAAnB,QAAA,gBAChDpG,OAAA,CAACtC,WAAW;kBAAC+J,SAAS;kBAAArB,QAAA,gBACpBpG,OAAA,CAACrC,UAAU;oBAAAyI,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC3CxG,OAAA,CAACpC,MAAM;oBACL6G,KAAK,EAAE/D,QAAQ,CAACc,OAAO,CAACC,kBAAmB;oBAC3CiG,QAAQ,EAAGC,CAAC,IAAKrD,mBAAmB,CAAC,SAAS,EAAE,oBAAoB,EAAEqD,CAAC,CAACC,MAAM,CAACnD,KAAK,CAAE;oBACtFyB,KAAK,EAAC,oBAAoB;oBAAAE,QAAA,gBAE1BpG,OAAA,CAACnC,QAAQ;sBAAC4G,KAAK,EAAC,QAAQ;sBAAA2B,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eAC1CxG,OAAA,CAACnC,QAAQ;sBAAC4G,KAAK,EAAC,SAAS;sBAAA2B,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAEdxG,OAAA,CAACtC,WAAW;kBAAC+J,SAAS;kBAAArB,QAAA,gBACpBpG,OAAA,CAACrC,UAAU;oBAAAyI,QAAA,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC7CxG,OAAA,CAACpC,MAAM;oBACL6G,KAAK,EAAE/D,QAAQ,CAACc,OAAO,CAACE,oBAAqB;oBAC7CgG,QAAQ,EAAGC,CAAC,IAAKrD,mBAAmB,CAAC,SAAS,EAAE,sBAAsB,EAAEqD,CAAC,CAACC,MAAM,CAACnD,KAAK,CAAE;oBACxFyB,KAAK,EAAC,sBAAsB;oBAAAE,QAAA,gBAE5BpG,OAAA,CAACnC,QAAQ;sBAAC4G,KAAK,EAAC,QAAQ;sBAAA2B,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eAC1CxG,OAAA,CAACnC,QAAQ;sBAAC4G,KAAK,EAAC,SAAS;sBAAA2B,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eAENxG,OAAA,CAACpB,OAAO;gBAACyJ,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE;cAAE;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAE1BxG,OAAA,CAAC/B,IAAI;gBAAAmI,QAAA,gBACHpG,OAAA,CAAC9B,QAAQ;kBAAAkI,QAAA,gBACPpG,OAAA,CAAC7B,YAAY;oBACX8J,OAAO,EAAC,cAAc;oBACtBC,SAAS,EAAC;kBAA4C;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC,eACFxG,OAAA,CAAC5B,uBAAuB;oBAAAgI,QAAA,eACtBpG,OAAA,CAAClC,MAAM;sBACLqK,OAAO,EAAEzH,QAAQ,CAACc,OAAO,CAACG,YAAa;sBACvC+F,QAAQ,EAAGC,CAAC,IAAKrD,mBAAmB,CAAC,SAAS,EAAE,cAAc,EAAEqD,CAAC,CAACC,MAAM,CAACO,OAAO;oBAAE;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACqB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eAEXxG,OAAA,CAAC9B,QAAQ;kBAAAkI,QAAA,gBACPpG,OAAA,CAAC7B,YAAY;oBACX8J,OAAO,EAAC,WAAW;oBACnBC,SAAS,EAAC;kBAA+C;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC,eACFxG,OAAA,CAAC5B,uBAAuB;oBAAAgI,QAAA,eACtBpG,OAAA,CAAClC,MAAM;sBACLqK,OAAO,EAAEzH,QAAQ,CAACc,OAAO,CAACI,SAAU;sBACpC8F,QAAQ,EAAGC,CAAC,IAAKrD,mBAAmB,CAAC,SAAS,EAAE,WAAW,EAAEqD,CAAC,CAACC,MAAM,CAACO,OAAO;oBAAE;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACqB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGPxG,OAAA,CAACzC,IAAI;UAAC2J,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eACvBpG,OAAA,CAACxC,IAAI;YAAA4I,QAAA,eACHpG,OAAA,CAACvC,WAAW;cAAA2I,QAAA,gBACVpG,OAAA,CAAC3C,GAAG;gBAACgK,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,GAAG,EAAE,CAAE;gBAACZ,EAAE,EAAE,CAAE;gBAAAP,QAAA,gBACpDpG,OAAA,CAACb,OAAO;kBAAC4H,KAAK,EAAC;gBAAS;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3BxG,OAAA,CAAC1C,UAAU;kBAACsJ,OAAO,EAAC,IAAI;kBAAAR,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eAENxG,OAAA,CAAC1C,UAAU;gBAACsJ,OAAO,EAAC,OAAO;gBAACG,KAAK,EAAC,gBAAgB;gBAACJ,EAAE,EAAE,CAAE;gBAAAP,QAAA,EAAC;cAE1D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAEbxG,OAAA,CAAC3C,GAAG;gBAACgK,OAAO,EAAC,MAAM;gBAACG,aAAa,EAAC,QAAQ;gBAACD,GAAG,EAAE,CAAE;gBAAAnB,QAAA,gBAChDpG,OAAA,CAACjC,MAAM;kBACL6I,OAAO,EAAC,UAAU;kBAClB2B,SAAS,eAAEvI,OAAA,CAACb,OAAO;oBAAAkH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACvBgC,OAAO,EAAEA,CAAA,KAAMtG,iBAAiB,CAAC,IAAI,CAAE;kBACvCuF,SAAS;kBAAArB,QAAA,EACV;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAETxG,OAAA,CAACjC,MAAM;kBACL6I,OAAO,EAAC,UAAU;kBAClBG,KAAK,EAAC,SAAS;kBACfyB,OAAO,EAAEA,CAAA,KAAMpE,yBAAyB,CAACS,MAAM,CAAC,CAAE;kBAClDuD,QAAQ,EAAEhE,yBAAyB,CAAC7B,SAAU;kBAC9CkF,SAAS;kBAAArB,QAAA,EAERhC,yBAAyB,CAAC7B,SAAS,GAAG,aAAa,GAAG;gBAAoB;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGPxG,OAAA,CAACzC,IAAI;UAAC2J,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eACvBpG,OAAA,CAACxC,IAAI;YAAA4I,QAAA,eACHpG,OAAA,CAACvC,WAAW;cAAA2I,QAAA,gBACVpG,OAAA,CAAC3C,GAAG;gBAACgK,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,GAAG,EAAE,CAAE;gBAACZ,EAAE,EAAE,CAAE;gBAAAP,QAAA,gBACpDpG,OAAA,CAACd,QAAQ;kBAAC6H,KAAK,EAAC;gBAAS;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5BxG,OAAA,CAAC1C,UAAU;kBAACsJ,OAAO,EAAC,IAAI;kBAAAR,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eAENxG,OAAA,CAAC1C,UAAU;gBAACsJ,OAAO,EAAC,OAAO;gBAACG,KAAK,EAAC,gBAAgB;gBAACJ,EAAE,EAAE,CAAE;gBAAAP,QAAA,EAAC;cAE1D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAEbxG,OAAA,CAAC3C,GAAG;gBAACgK,OAAO,EAAC,MAAM;gBAACG,aAAa,EAAC,QAAQ;gBAACD,GAAG,EAAE,CAAE;gBAAAnB,QAAA,eAChDpG,OAAA,CAACjC,MAAM;kBACL6I,OAAO,EAAC,UAAU;kBAClB2B,SAAS,eAAEvI,OAAA,CAACd,QAAQ;oBAAAmH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACxBgC,OAAO,EAAE1D,gBAAiB;kBAC1BsD,QAAQ,EAAEjG,aAAc;kBACxBsF,SAAS;kBAAArB,QAAA,EAERjE,aAAa,GAAG,cAAc,GAAG;gBAAgB;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGPxG,OAAA,CAACzC,IAAI;UAAC2J,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eACvBpG,OAAA,CAACxC,IAAI;YAAA4I,QAAA,eACHpG,OAAA,CAACvC,WAAW;cAAA2I,QAAA,gBACVpG,OAAA,CAAC3C,GAAG;gBAACgK,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,GAAG,EAAE,CAAE;gBAACZ,EAAE,EAAE,CAAE;gBAAAP,QAAA,gBACpDpG,OAAA,CAAChB,MAAM;kBAAC+H,KAAK,EAAC;gBAAO;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxBxG,OAAA,CAAC1C,UAAU;kBAACsJ,OAAO,EAAC,IAAI;kBAACG,KAAK,EAAC,OAAO;kBAAAX,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,eAENxG,OAAA,CAAChC,KAAK;gBAACyK,QAAQ,EAAC,SAAS;gBAACJ,EAAE,EAAE;kBAAE1B,EAAE,EAAE;gBAAE,CAAE;gBAAAP,QAAA,EAAC;cAEzC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAERxG,OAAA,CAACjC,MAAM;gBACL6I,OAAO,EAAC,UAAU;gBAClBG,KAAK,EAAC,OAAO;gBACbwB,SAAS,eAAEvI,OAAA,CAAChB,MAAM;kBAAAqH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACtBgC,OAAO,EAAEA,CAAA,KAAM1G,sBAAsB,CAAC,IAAI,CAAE;gBAC5C2F,SAAS;gBAAArB,QAAA,EACV;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPxG,OAAA,CAAC3C,GAAG;QAACqL,EAAE,EAAE,CAAE;QAACrB,OAAO,EAAC,MAAM;QAACsB,cAAc,EAAC,UAAU;QAAAvC,QAAA,eAClDpG,OAAA,CAACjC,MAAM;UACL6I,OAAO,EAAC,WAAW;UACnB2B,SAAS,eAAEvI,OAAA,CAACf,IAAI;YAAAoH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACpBgC,OAAO,EAAE5D,kBAAmB;UAC5BwD,QAAQ,EAAEjF,sBAAsB,CAACZ,SAAU;UAAA6D,QAAA,EAE1CjD,sBAAsB,CAACZ,SAAS,GAAG,WAAW,GAAG;QAAe;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNxG,OAAA,CAAC3B,MAAM;QACLuK,IAAI,EAAE/G,mBAAoB;QAC1BgH,OAAO,EAAEA,CAAA,KAAM/G,sBAAsB,CAAC,KAAK,CAAE;QAC7CgH,QAAQ,EAAC,IAAI;QACbrB,SAAS;QAAArB,QAAA,gBAETpG,OAAA,CAAC1B,WAAW;UAACyI,KAAK,EAAC,OAAO;UAAAX,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACvDxG,OAAA,CAACzB,aAAa;UAAA6H,QAAA,gBACZpG,OAAA,CAAChC,KAAK;YAACyK,QAAQ,EAAC,OAAO;YAACJ,EAAE,EAAE;cAAE1B,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,EAAC;UAGvC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAERxG,OAAA,CAAC1C,UAAU;YAACsJ,OAAO,EAAC,OAAO;YAACE,YAAY;YAAAV,QAAA,EAAC;UAEzC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEbxG,OAAA,CAACvB,SAAS;YACRgJ,SAAS;YACThD,KAAK,EAAE1C,kBAAmB;YAC1B2F,QAAQ,EAAGC,CAAC,IAAK3F,qBAAqB,CAAC2F,CAAC,CAACC,MAAM,CAACnD,KAAK,CAAE;YACvDsE,WAAW,EAAC,wBAAwB;YACpCV,EAAE,EAAE;cAAEK,EAAE,EAAE;YAAE;UAAE;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC,eAChBxG,OAAA,CAACxB,aAAa;UAAA4H,QAAA,gBACZpG,OAAA,CAACjC,MAAM;YAACyK,OAAO,EAAEA,CAAA,KAAM1G,sBAAsB,CAAC,KAAK,CAAE;YAAAsE,QAAA,EAAC;UAEtD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTxG,OAAA,CAACjC,MAAM;YACLgJ,KAAK,EAAC,OAAO;YACbH,OAAO,EAAC,WAAW;YACnBwB,QAAQ,EAAErG,kBAAkB,KAAK,QAAQ,IAAIwB,qBAAqB,CAAChB,SAAU;YAC7EiG,OAAO,EAAExC,mBAAoB;YAC7BuC,SAAS,EAAEhF,qBAAqB,CAAChB,SAAS,gBAAGvC,OAAA,CAACtB,gBAAgB;cAACsK,IAAI,EAAE;YAAG;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGxG,OAAA,CAAChB,MAAM;cAAAqH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EAExF7C,qBAAqB,CAAChB,SAAS,GAAG,aAAa,GAAG;UAAgB;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGTxG,OAAA,CAAC3B,MAAM;QACLuK,IAAI,EAAE3G,cAAe;QACrB4G,OAAO,EAAEA,CAAA,KAAM3G,iBAAiB,CAAC,KAAK,CAAE;QACxC4G,QAAQ,EAAC,IAAI;QACbrB,SAAS;QAAArB,QAAA,gBAETpG,OAAA,CAAC1B,WAAW;UAAA8H,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC1CxG,OAAA,CAACzB,aAAa;UAAA6H,QAAA,EACXvD,cAAc,IAAIA,cAAc,CAACoG,MAAM,GAAG,CAAC,gBAC1CjJ,OAAA,CAAC/B,IAAI;YAAAmI,QAAA,EACFvD,cAAc,CAACgF,GAAG,CAAEqB,OAAY,iBAC/BlJ,OAAA,CAAC9B,QAAQ;cAAkBiL,OAAO;cAAA/C,QAAA,gBAChCpG,OAAA,CAAC7B,YAAY;gBACX8J,OAAO,eACLjI,OAAA,CAAC3C,GAAG;kBAACgK,OAAO,EAAC,MAAM;kBAACC,UAAU,EAAC,QAAQ;kBAACC,GAAG,EAAE,CAAE;kBAAAnB,QAAA,gBAC7CpG,OAAA,CAAC1C,UAAU;oBAACsJ,OAAO,EAAC,OAAO;oBAAAR,QAAA,EACxB8C,OAAO,CAACE,WAAW,IAAI;kBAAgB;oBAAA/C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,EACZ0C,OAAO,CAACG,UAAU,iBACjBrJ,OAAA,CAACrB,IAAI;oBAACuH,KAAK,EAAC,SAAS;oBAACa,KAAK,EAAC,SAAS;oBAACiC,IAAI,EAAC;kBAAO;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CACrD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;gBACD0B,SAAS,eACPlI,OAAA,CAAC3C,GAAG;kBAAA+I,QAAA,gBACFpG,OAAA,CAAC1C,UAAU;oBAACsJ,OAAO,EAAC,OAAO;oBAACG,KAAK,EAAC,gBAAgB;oBAAAX,QAAA,GAAC,MAC7C,EAAC8C,OAAO,CAACI,UAAU;kBAAA;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC,eACbxG,OAAA,CAAC1C,UAAU;oBAACsJ,OAAO,EAAC,OAAO;oBAACG,KAAK,EAAC,gBAAgB;oBAAAX,QAAA,GAAC,eACpC,EAAC,IAAIZ,IAAI,CAAC0D,OAAO,CAACK,aAAa,CAAC,CAACC,cAAc,CAAC,CAAC;kBAAA;oBAAAnD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC,eACbxG,OAAA,CAAC1C,UAAU;oBAACsJ,OAAO,EAAC,OAAO;oBAACG,KAAK,EAAC,gBAAgB;oBAAAX,QAAA,GAAC,YACvC,EAAC8C,OAAO,CAACvF,QAAQ,IAAI,SAAS;kBAAA;oBAAA0C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFxG,OAAA,CAAC5B,uBAAuB;gBAAAgI,QAAA,EACrB,CAAC8C,OAAO,CAACG,UAAU,iBAClBrJ,OAAA,CAACjC,MAAM;kBACLgJ,KAAK,EAAC,OAAO;kBACbiC,IAAI,EAAC,OAAO;kBACZR,OAAO,EAAEA,CAAA,KAAMvE,qBAAqB,CAACY,MAAM,CAACqE,OAAO,CAACO,EAAE,CAAE;kBACxDrB,QAAQ,EAAEnE,qBAAqB,CAAC1B,SAAU;kBAAA6D,QAAA,EAC3C;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cACT;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACsB,CAAC;YAAA,GArCb0C,OAAO,CAACO,EAAE;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsCf,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,gBAEPxG,OAAA,CAAC1C,UAAU;YAAA8I,QAAA,EAAC;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAClD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBxG,OAAA,CAACxB,aAAa;UAAA4H,QAAA,eACZpG,OAAA,CAACjC,MAAM;YAACyK,OAAO,EAAEA,CAAA,KAAMtG,iBAAiB,CAAC,KAAK,CAAE;YAAAkE,QAAA,EAAC;UAEjD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACpG,EAAA,CA7oBID,YAAsB;EAAA,QACTP,YAAY,EACTF,cAAc,EACcG,QAAQ,EA8BGF,QAAQ,EAcRA,QAAQ,EAWpCF,WAAW,EAaZA,WAAW,EAeXA,WAAW,EAaPA,WAAW;AAAA;AAAAiK,EAAA,GAnGzCvJ,YAAsB;AA+oB5B,eAAeA,YAAY;AAAC,IAAAuJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}