{"ast": null, "code": "// TrustVault - API Service\n\nimport axios from 'axios';\nimport { toast } from 'react-hot-toast';\n\n// Types\n\nclass ApiService {\n  constructor() {\n    this.api = void 0;\n    this.baseURL = void 0;\n    // Authentication endpoints\n    this.login = async credentials => {\n      const response = await this.api.post('/auth/login/', credentials);\n      if (response.data.access_token) {\n        this.setTokens(response.data.access_token, response.data.refresh_token);\n      }\n      return response.data;\n    };\n    this.register = async data => {\n      const response = await this.api.post('/auth/register/', data);\n      return response.data;\n    };\n    this.logout = async () => {\n      const refreshToken = this.getRefreshToken();\n      try {\n        await this.api.post('/auth/logout/', {\n          refresh_token: refreshToken\n        });\n      } finally {\n        this.clearTokens();\n      }\n    };\n    this.getCurrentUser = async () => {\n      const response = await this.api.get('/auth/profile/');\n      return response.data;\n    };\n    this.updateProfile = async data => {\n      const response = await this.api.put('/auth/profile/', data);\n      return response.data;\n    };\n    this.changePassword = async data => {\n      const response = await this.api.post('/auth/change-password/', data);\n      return response.data;\n    };\n    // MFA endpoints\n    this.setupMFA = async () => {\n      const response = await this.api.post('/auth/mfa/setup/');\n      return response.data;\n    };\n    this.verifyMFA = async token => {\n      const response = await this.api.post('/auth/mfa/verify/', {\n        token\n      });\n      return response.data;\n    };\n    this.disableMFA = async () => {\n      const response = await this.api.post('/auth/mfa/disable/');\n      return response.data;\n    };\n    // Settings endpoints\n    this.getUserSettings = async () => {\n      const response = await this.api.get('/auth/settings/');\n      return response.data;\n    };\n    this.updateUserSettings = async settings => {\n      const response = await this.api.put('/auth/settings/', settings);\n      return response.data;\n    };\n    this.exportUserData = async () => {\n      const response = await this.api.get('/auth/export-data/', {\n        responseType: 'blob'\n      });\n      return response.data;\n    };\n    this.deleteAccount = async confirmation => {\n      const response = await this.api.post('/auth/delete-account/', {\n        confirmation\n      });\n      return response.data;\n    };\n    // Session management\n    this.getActiveSessions = async () => {\n      const response = await this.api.get('/auth/sessions/');\n      return response.data;\n    };\n    this.revokeSession = async sessionId => {\n      const response = await this.api.delete(`/auth/sessions/${sessionId}/`);\n      return response.data;\n    };\n    this.revokeAllSessions = async () => {\n      const response = await this.api.post('/auth/sessions/revoke-all/');\n      return response.data;\n    };\n    // API Key management\n    this.getApiKeys = async () => {\n      const response = await this.api.get('/auth/api-keys/');\n      return response.data;\n    };\n    this.createApiKey = async (name, permissions) => {\n      const response = await this.api.post('/auth/api-keys/', {\n        name,\n        permissions\n      });\n      return response.data;\n    };\n    this.revokeApiKey = async keyId => {\n      const response = await this.api.delete(`/auth/api-keys/${keyId}/`);\n      return response.data;\n    };\n    // Portfolio endpoints\n    this.getPortfolios = async () => {\n      const response = await this.api.get('/portfolio/');\n      // Handle both paginated and non-paginated responses\n      if (response.data && typeof response.data === 'object' && 'results' in response.data) {\n        return response.data.results;\n      }\n      return response.data;\n    };\n    this.getPortfolio = async id => {\n      const response = await this.api.get(`/portfolio/${id}/`);\n      return response.data;\n    };\n    this.createPortfolio = async data => {\n      const response = await this.api.post('/portfolio/', data);\n      return response.data;\n    };\n    this.updatePortfolio = async (id, data) => {\n      const response = await this.api.put(`/portfolio/${id}/`, data);\n      return response.data;\n    };\n    this.deletePortfolio = async id => {\n      await this.api.delete(`/portfolio/${id}/`);\n    };\n    // Asset endpoints\n    this.getAssets = async params => {\n      const response = await this.api.get('/portfolio/assets/', {\n        params\n      });\n      // Handle both paginated and non-paginated responses\n      if (response.data && typeof response.data === 'object' && 'results' in response.data) {\n        return response.data.results;\n      }\n      return response.data;\n    };\n    // Holdings endpoints\n    this.getHoldings = async portfolioId => {\n      const response = await this.api.get(`/portfolio/${portfolioId}/holdings/`);\n      // Handle both paginated and non-paginated responses\n      if (response.data && typeof response.data === 'object' && 'results' in response.data) {\n        return response.data.results;\n      }\n      return response.data;\n    };\n    this.createHolding = async (portfolioId, data) => {\n      const response = await this.api.post(`/portfolio/${portfolioId}/holdings/`, data);\n      return response.data;\n    };\n    // Transaction endpoints\n    this.getTransactions = async portfolioId => {\n      const response = await this.api.get(`/portfolio/${portfolioId}/transactions/`);\n      // Handle both paginated and non-paginated responses\n      if (response.data && typeof response.data === 'object' && 'results' in response.data) {\n        return response.data.results;\n      }\n      return response.data;\n    };\n    this.createTransaction = async (portfolioId, data) => {\n      const response = await this.api.post(`/portfolio/${portfolioId}/transactions/`, data);\n      return response.data;\n    };\n    // Analytics endpoints\n    this.getPortfolioAnalytics = async portfolioId => {\n      const response = await this.api.get(`/portfolio/${portfolioId}/analytics/`);\n      return response.data;\n    };\n    // Security endpoints\n    this.getSecurityDashboard = async () => {\n      const response = await this.api.get('/security/dashboard/');\n      return response.data;\n    };\n    this.getSecurityEvents = async params => {\n      const response = await this.api.get('/security/events/', {\n        params\n      });\n      return response.data;\n    };\n    this.getAuditLogs = async params => {\n      const response = await this.api.get('/security/audit-logs/', {\n        params\n      });\n      return response.data;\n    };\n    // Health check\n    this.healthCheck = async () => {\n      const response = await this.api.get('/core/status/');\n      return response.data;\n    };\n    // Generic request method\n    this.request = async config => {\n      const response = await this.api.request(config);\n      return response.data;\n    };\n    this.baseURL = process.env.REACT_APP_API_URL || '/api/v1';\n    this.api = axios.create({\n      baseURL: this.baseURL,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n    this.setupInterceptors();\n  }\n  setupInterceptors() {\n    // Request interceptor\n    this.api.interceptors.request.use(config => {\n      // Add auth token if available\n      const token = this.getAccessToken();\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n\n      // Add security headers\n      config.headers['X-Requested-With'] = 'XMLHttpRequest';\n      config.headers['X-Client-Version'] = '1.0.0';\n\n      // Log request in development\n      if (process.env.NODE_ENV === 'development') {\n        var _config$method;\n        console.log(`API Request: ${(_config$method = config.method) === null || _config$method === void 0 ? void 0 : _config$method.toUpperCase()} ${config.url}`);\n      }\n      return config;\n    }, error => {\n      return Promise.reject(error);\n    });\n\n    // Response interceptor\n    this.api.interceptors.response.use(response => {\n      return response;\n    }, async error => {\n      var _error$response;\n      const originalRequest = error.config;\n\n      // Handle 401 errors (unauthorized)\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401 && !originalRequest._retry) {\n        originalRequest._retry = true;\n        try {\n          // Try to refresh token\n          const refreshToken = this.getRefreshToken();\n          if (refreshToken) {\n            const response = await this.refreshAccessToken(refreshToken);\n            this.setTokens(response.data.access_token, refreshToken);\n\n            // Retry original request\n            originalRequest.headers.Authorization = `Bearer ${response.data.access_token}`;\n            return this.api(originalRequest);\n          }\n        } catch (refreshError) {\n          // Refresh failed, redirect to login\n          this.clearTokens();\n          window.location.href = '/login';\n          return Promise.reject(refreshError);\n        }\n      }\n\n      // Handle other errors\n      this.handleApiError(error);\n      return Promise.reject(error);\n    });\n  }\n  handleApiError(error) {\n    var _error$response2, _error$response2$data, _error$response3;\n    const message = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || error.message || 'An error occurred';\n\n    // Don't show toast for certain errors\n    const silentErrors = [401, 403];\n    if (!silentErrors.includes((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.status)) {\n      toast.error(message);\n    }\n\n    // Log error in development\n    if (process.env.NODE_ENV === 'development') {\n      var _error$response4;\n      console.error('API Error:', ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.data) || error.message);\n    }\n  }\n\n  // Token management\n  getAccessToken() {\n    return localStorage.getItem('access_token');\n  }\n  getRefreshToken() {\n    return localStorage.getItem('refresh_token');\n  }\n  setTokens(accessToken, refreshToken) {\n    localStorage.setItem('access_token', accessToken);\n    localStorage.setItem('refresh_token', refreshToken);\n  }\n  clearTokens() {\n    localStorage.removeItem('access_token');\n    localStorage.removeItem('refresh_token');\n  }\n  async refreshAccessToken(refreshToken) {\n    return this.api.post('/auth/token/refresh/', {\n      refresh: refreshToken\n    });\n  }\n}\n\n// Create singleton instance\nconst apiService = new ApiService();\nexport default apiService;", "map": {"version": 3, "names": ["axios", "toast", "ApiService", "constructor", "api", "baseURL", "login", "credentials", "response", "post", "data", "access_token", "setTokens", "refresh_token", "register", "logout", "refreshToken", "getRefreshToken", "clearTokens", "getCurrentUser", "get", "updateProfile", "put", "changePassword", "setupMFA", "verifyMFA", "token", "disableMFA", "getUserSettings", "updateUserSettings", "settings", "exportUserData", "responseType", "deleteAccount", "confirmation", "getActiveSessions", "revokeSession", "sessionId", "delete", "revokeAllSessions", "getApi<PERSON>eys", "createApiKey", "name", "permissions", "revokeApiKey", "keyId", "getPortfolios", "results", "getPortfolio", "id", "createPortfolio", "updatePortfolio", "deletePortfolio", "getAssets", "params", "getHoldings", "portfolioId", "createHolding", "getTransactions", "createTransaction", "getPortfolioAnalytics", "getSecurityDashboard", "getSecurityEvents", "getAuditLogs", "healthCheck", "request", "config", "process", "env", "REACT_APP_API_URL", "create", "timeout", "headers", "setupInterceptors", "interceptors", "use", "getAccessToken", "Authorization", "NODE_ENV", "_config$method", "console", "log", "method", "toUpperCase", "url", "error", "Promise", "reject", "_error$response", "originalRequest", "status", "_retry", "refreshAccessToken", "refreshError", "window", "location", "href", "handleApiError", "_error$response2", "_error$response2$data", "_error$response3", "message", "silentErrors", "includes", "_error$response4", "localStorage", "getItem", "accessToken", "setItem", "removeItem", "refresh", "apiService"], "sources": ["C:/Users/<USER>/Documents/augment-projects/TrustVault/frontend/src/services/api.ts"], "sourcesContent": ["// TrustVault - API Service\n\nimport axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';\nimport { toast } from 'react-hot-toast';\n\n// Types\nimport {\n  AuthResponse,\n  LoginCredentials,\n  RegisterData,\n  User,\n  Portfolio,\n  Asset,\n  Transaction,\n  SecurityDashboard,\n  ChangePasswordData,\n  PaginatedResponse,\n} from '../types';\n\nclass ApiService {\n  private api: AxiosInstance;\n  private baseURL: string;\n\n  constructor() {\n    this.baseURL = process.env.REACT_APP_API_URL || '/api/v1';\n    \n    this.api = axios.create({\n      baseURL: this.baseURL,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    this.setupInterceptors();\n  }\n\n  private setupInterceptors(): void {\n    // Request interceptor\n    this.api.interceptors.request.use(\n      (config) => {\n        // Add auth token if available\n        const token = this.getAccessToken();\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n\n        // Add security headers\n        config.headers['X-Requested-With'] = 'XMLHttpRequest';\n        config.headers['X-Client-Version'] = '1.0.0';\n\n        // Log request in development\n        if (process.env.NODE_ENV === 'development') {\n          console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);\n        }\n\n        return config;\n      },\n      (error) => {\n        return Promise.reject(error);\n      }\n    );\n\n    // Response interceptor\n    this.api.interceptors.response.use(\n      (response: AxiosResponse) => {\n        return response;\n      },\n      async (error) => {\n        const originalRequest = error.config;\n\n        // Handle 401 errors (unauthorized)\n        if (error.response?.status === 401 && !originalRequest._retry) {\n          originalRequest._retry = true;\n\n          try {\n            // Try to refresh token\n            const refreshToken = this.getRefreshToken();\n            if (refreshToken) {\n              const response = await this.refreshAccessToken(refreshToken);\n              this.setTokens(response.data.access_token, refreshToken);\n              \n              // Retry original request\n              originalRequest.headers.Authorization = `Bearer ${response.data.access_token}`;\n              return this.api(originalRequest);\n            }\n          } catch (refreshError) {\n            // Refresh failed, redirect to login\n            this.clearTokens();\n            window.location.href = '/login';\n            return Promise.reject(refreshError);\n          }\n        }\n\n        // Handle other errors\n        this.handleApiError(error);\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  private handleApiError(error: any): void {\n    const message = error.response?.data?.message || error.message || 'An error occurred';\n    \n    // Don't show toast for certain errors\n    const silentErrors = [401, 403];\n    if (!silentErrors.includes(error.response?.status)) {\n      toast.error(message);\n    }\n\n    // Log error in development\n    if (process.env.NODE_ENV === 'development') {\n      console.error('API Error:', error.response?.data || error.message);\n    }\n  }\n\n  // Token management\n  private getAccessToken(): string | null {\n    return localStorage.getItem('access_token');\n  }\n\n  private getRefreshToken(): string | null {\n    return localStorage.getItem('refresh_token');\n  }\n\n  private setTokens(accessToken: string, refreshToken: string): void {\n    localStorage.setItem('access_token', accessToken);\n    localStorage.setItem('refresh_token', refreshToken);\n  }\n\n  private clearTokens(): void {\n    localStorage.removeItem('access_token');\n    localStorage.removeItem('refresh_token');\n  }\n\n  private async refreshAccessToken(refreshToken: string): Promise<AxiosResponse> {\n    return this.api.post('/auth/token/refresh/', {\n      refresh: refreshToken,\n    });\n  }\n\n  // Authentication endpoints\n  login = async (credentials: LoginCredentials): Promise<AuthResponse> => {\n    const response = await this.api.post<AuthResponse>('/auth/login/', credentials);\n\n    if (response.data.access_token) {\n      this.setTokens(response.data.access_token, response.data.refresh_token);\n    }\n\n    return response.data;\n  }\n\n  register = async (data: RegisterData): Promise<{ message: string; user_id: string }> => {\n    const response = await this.api.post('/auth/register/', data);\n    return response.data;\n  }\n\n  logout = async (): Promise<void> => {\n    const refreshToken = this.getRefreshToken();\n\n    try {\n      await this.api.post('/auth/logout/', {\n        refresh_token: refreshToken,\n      });\n    } finally {\n      this.clearTokens();\n    }\n  }\n\n  getCurrentUser = async (): Promise<User> => {\n    const response = await this.api.get<User>('/auth/profile/');\n    return response.data;\n  }\n\n  updateProfile = async (data: Partial<User>): Promise<User> => {\n    const response = await this.api.put<User>('/auth/profile/', data);\n    return response.data;\n  }\n\n  changePassword = async (data: ChangePasswordData): Promise<{ message: string }> => {\n    const response = await this.api.post('/auth/change-password/', data);\n    return response.data;\n  }\n\n  // MFA endpoints\n  setupMFA = async (): Promise<{ qr_code_url: string; secret_key: string; message: string }> => {\n    const response = await this.api.post('/auth/mfa/setup/');\n    return response.data;\n  }\n\n  verifyMFA = async (token: string): Promise<{ message: string }> => {\n    const response = await this.api.post('/auth/mfa/verify/', { token });\n    return response.data;\n  }\n\n  disableMFA = async (): Promise<{ message: string }> => {\n    const response = await this.api.post('/auth/mfa/disable/');\n    return response.data;\n  }\n\n  // Settings endpoints\n  getUserSettings = async (): Promise<any> => {\n    const response = await this.api.get('/auth/settings/');\n    return response.data;\n  }\n\n  updateUserSettings = async (settings: any): Promise<any> => {\n    const response = await this.api.put('/auth/settings/', settings);\n    return response.data;\n  }\n\n  exportUserData = async (): Promise<Blob> => {\n    const response = await this.api.get('/auth/export-data/', {\n      responseType: 'blob',\n    });\n    return response.data;\n  }\n\n  deleteAccount = async (confirmation: string): Promise<{ message: string }> => {\n    const response = await this.api.post('/auth/delete-account/', {\n      confirmation,\n    });\n    return response.data;\n  }\n\n  // Session management\n  getActiveSessions = async (): Promise<any[]> => {\n    const response = await this.api.get('/auth/sessions/');\n    return response.data;\n  }\n\n  revokeSession = async (sessionId: string): Promise<{ message: string }> => {\n    const response = await this.api.delete(`/auth/sessions/${sessionId}/`);\n    return response.data;\n  }\n\n  revokeAllSessions = async (): Promise<{ message: string }> => {\n    const response = await this.api.post('/auth/sessions/revoke-all/');\n    return response.data;\n  }\n\n  // API Key management\n  getApiKeys = async (): Promise<any[]> => {\n    const response = await this.api.get('/auth/api-keys/');\n    return response.data;\n  }\n\n  createApiKey = async (name: string, permissions: string[]): Promise<any> => {\n    const response = await this.api.post('/auth/api-keys/', {\n      name,\n      permissions,\n    });\n    return response.data;\n  }\n\n  revokeApiKey = async (keyId: string): Promise<{ message: string }> => {\n    const response = await this.api.delete(`/auth/api-keys/${keyId}/`);\n    return response.data;\n  }\n\n  // Portfolio endpoints\n  getPortfolios = async (): Promise<Portfolio[]> => {\n    const response = await this.api.get<PaginatedResponse<Portfolio> | Portfolio[]>('/portfolio/');\n    // Handle both paginated and non-paginated responses\n    if (response.data && typeof response.data === 'object' && 'results' in response.data) {\n      return response.data.results;\n    }\n    return response.data as Portfolio[];\n  }\n\n  getPortfolio = async (id: string): Promise<Portfolio> => {\n    const response = await this.api.get<Portfolio>(`/portfolio/${id}/`);\n    return response.data;\n  }\n\n  createPortfolio = async (data: Partial<Portfolio>): Promise<Portfolio> => {\n    const response = await this.api.post<Portfolio>('/portfolio/', data);\n    return response.data;\n  }\n\n  updatePortfolio = async (id: string, data: Partial<Portfolio>): Promise<Portfolio> => {\n    const response = await this.api.put<Portfolio>(`/portfolio/${id}/`, data);\n    return response.data;\n  }\n\n  deletePortfolio = async (id: string): Promise<void> => {\n    await this.api.delete(`/portfolio/${id}/`);\n  }\n\n  // Asset endpoints\n  getAssets = async (params?: {\n    type?: string;\n    search?: string;\n    sector?: string;\n  }): Promise<Asset[]> => {\n    const response = await this.api.get<PaginatedResponse<Asset> | Asset[]>('/portfolio/assets/', { params });\n    // Handle both paginated and non-paginated responses\n    if (response.data && typeof response.data === 'object' && 'results' in response.data) {\n      return response.data.results;\n    }\n    return response.data as Asset[];\n  }\n\n  // Holdings endpoints\n  getHoldings = async (portfolioId: string): Promise<any[]> => {\n    const response = await this.api.get<PaginatedResponse<any> | any[]>(`/portfolio/${portfolioId}/holdings/`);\n    // Handle both paginated and non-paginated responses\n    if (response.data && typeof response.data === 'object' && 'results' in response.data) {\n      return response.data.results;\n    }\n    return response.data as any[];\n  }\n\n  createHolding = async (portfolioId: string, data: any): Promise<any> => {\n    const response = await this.api.post<any>(`/portfolio/${portfolioId}/holdings/`, data);\n    return response.data;\n  }\n\n  // Transaction endpoints\n  getTransactions = async (portfolioId: string): Promise<Transaction[]> => {\n    const response = await this.api.get<PaginatedResponse<Transaction> | Transaction[]>(`/portfolio/${portfolioId}/transactions/`);\n    // Handle both paginated and non-paginated responses\n    if (response.data && typeof response.data === 'object' && 'results' in response.data) {\n      return response.data.results;\n    }\n    return response.data as Transaction[];\n  }\n\n  createTransaction = async (portfolioId: string, data: Partial<Transaction>): Promise<Transaction> => {\n    const response = await this.api.post<Transaction>(`/portfolio/${portfolioId}/transactions/`, data);\n    return response.data;\n  }\n\n  // Analytics endpoints\n  getPortfolioAnalytics = async (portfolioId: string): Promise<any> => {\n    const response = await this.api.get(`/portfolio/${portfolioId}/analytics/`);\n    return response.data;\n  }\n\n  // Security endpoints\n  getSecurityDashboard = async (): Promise<SecurityDashboard> => {\n    const response = await this.api.get<SecurityDashboard>('/security/dashboard/');\n    return response.data;\n  }\n\n  getSecurityEvents = async (params?: {\n    risk_level?: string;\n    event_type?: string;\n    is_resolved?: boolean;\n    page?: number;\n  }): Promise<PaginatedResponse<any>> => {\n    const response = await this.api.get('/security/events/', { params });\n    return response.data;\n  }\n\n  getAuditLogs = async (params?: {\n    action?: string;\n    resource_type?: string;\n    user_id?: string;\n    severity?: string;\n    page?: number;\n  }): Promise<PaginatedResponse<any>> => {\n    const response = await this.api.get('/security/audit-logs/', { params });\n    return response.data;\n  }\n\n  // Health check\n  healthCheck = async (): Promise<{ status: string }> => {\n    const response = await this.api.get('/core/status/');\n    return response.data;\n  }\n\n  // Generic request method\n  request = async <T>(config: AxiosRequestConfig): Promise<T> => {\n    const response = await this.api.request<T>(config);\n    return response.data;\n  }\n}\n\n// Create singleton instance\nconst apiService = new ApiService();\n\nexport default apiService;\n"], "mappings": "AAAA;;AAEA,OAAOA,KAAK,MAA4D,OAAO;AAC/E,SAASC,KAAK,QAAQ,iBAAiB;;AAEvC;;AAcA,MAAMC,UAAU,CAAC;EAIfC,WAAWA,CAAA,EAAG;IAAA,KAHNC,GAAG;IAAA,KACHC,OAAO;IAwHf;IAAA,KACAC,KAAK,GAAG,MAAOC,WAA6B,IAA4B;MACtE,MAAMC,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACK,IAAI,CAAe,cAAc,EAAEF,WAAW,CAAC;MAE/E,IAAIC,QAAQ,CAACE,IAAI,CAACC,YAAY,EAAE;QAC9B,IAAI,CAACC,SAAS,CAACJ,QAAQ,CAACE,IAAI,CAACC,YAAY,EAAEH,QAAQ,CAACE,IAAI,CAACG,aAAa,CAAC;MACzE;MAEA,OAAOL,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAEDI,QAAQ,GAAG,MAAOJ,IAAkB,IAAoD;MACtF,MAAMF,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACK,IAAI,CAAC,iBAAiB,EAAEC,IAAI,CAAC;MAC7D,OAAOF,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAEDK,MAAM,GAAG,YAA2B;MAClC,MAAMC,YAAY,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;MAE3C,IAAI;QACF,MAAM,IAAI,CAACb,GAAG,CAACK,IAAI,CAAC,eAAe,EAAE;UACnCI,aAAa,EAAEG;QACjB,CAAC,CAAC;MACJ,CAAC,SAAS;QACR,IAAI,CAACE,WAAW,CAAC,CAAC;MACpB;IACF,CAAC;IAAA,KAEDC,cAAc,GAAG,YAA2B;MAC1C,MAAMX,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACgB,GAAG,CAAO,gBAAgB,CAAC;MAC3D,OAAOZ,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAEDW,aAAa,GAAG,MAAOX,IAAmB,IAAoB;MAC5D,MAAMF,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACkB,GAAG,CAAO,gBAAgB,EAAEZ,IAAI,CAAC;MACjE,OAAOF,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAEDa,cAAc,GAAG,MAAOb,IAAwB,IAAmC;MACjF,MAAMF,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACK,IAAI,CAAC,wBAAwB,EAAEC,IAAI,CAAC;MACpE,OAAOF,QAAQ,CAACE,IAAI;IACtB,CAAC;IAED;IAAA,KACAc,QAAQ,GAAG,YAAmF;MAC5F,MAAMhB,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACK,IAAI,CAAC,kBAAkB,CAAC;MACxD,OAAOD,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAEDe,SAAS,GAAG,MAAOC,KAAa,IAAmC;MACjE,MAAMlB,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACK,IAAI,CAAC,mBAAmB,EAAE;QAAEiB;MAAM,CAAC,CAAC;MACpE,OAAOlB,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAEDiB,UAAU,GAAG,YAA0C;MACrD,MAAMnB,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACK,IAAI,CAAC,oBAAoB,CAAC;MAC1D,OAAOD,QAAQ,CAACE,IAAI;IACtB,CAAC;IAED;IAAA,KACAkB,eAAe,GAAG,YAA0B;MAC1C,MAAMpB,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACgB,GAAG,CAAC,iBAAiB,CAAC;MACtD,OAAOZ,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAEDmB,kBAAkB,GAAG,MAAOC,QAAa,IAAmB;MAC1D,MAAMtB,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACkB,GAAG,CAAC,iBAAiB,EAAEQ,QAAQ,CAAC;MAChE,OAAOtB,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAEDqB,cAAc,GAAG,YAA2B;MAC1C,MAAMvB,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACgB,GAAG,CAAC,oBAAoB,EAAE;QACxDY,YAAY,EAAE;MAChB,CAAC,CAAC;MACF,OAAOxB,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAEDuB,aAAa,GAAG,MAAOC,YAAoB,IAAmC;MAC5E,MAAM1B,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACK,IAAI,CAAC,uBAAuB,EAAE;QAC5DyB;MACF,CAAC,CAAC;MACF,OAAO1B,QAAQ,CAACE,IAAI;IACtB,CAAC;IAED;IAAA,KACAyB,iBAAiB,GAAG,YAA4B;MAC9C,MAAM3B,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACgB,GAAG,CAAC,iBAAiB,CAAC;MACtD,OAAOZ,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAED0B,aAAa,GAAG,MAAOC,SAAiB,IAAmC;MACzE,MAAM7B,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACkC,MAAM,CAAC,kBAAkBD,SAAS,GAAG,CAAC;MACtE,OAAO7B,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAED6B,iBAAiB,GAAG,YAA0C;MAC5D,MAAM/B,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACK,IAAI,CAAC,4BAA4B,CAAC;MAClE,OAAOD,QAAQ,CAACE,IAAI;IACtB,CAAC;IAED;IAAA,KACA8B,UAAU,GAAG,YAA4B;MACvC,MAAMhC,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACgB,GAAG,CAAC,iBAAiB,CAAC;MACtD,OAAOZ,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAED+B,YAAY,GAAG,OAAOC,IAAY,EAAEC,WAAqB,KAAmB;MAC1E,MAAMnC,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACK,IAAI,CAAC,iBAAiB,EAAE;QACtDiC,IAAI;QACJC;MACF,CAAC,CAAC;MACF,OAAOnC,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAEDkC,YAAY,GAAG,MAAOC,KAAa,IAAmC;MACpE,MAAMrC,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACkC,MAAM,CAAC,kBAAkBO,KAAK,GAAG,CAAC;MAClE,OAAOrC,QAAQ,CAACE,IAAI;IACtB,CAAC;IAED;IAAA,KACAoC,aAAa,GAAG,YAAkC;MAChD,MAAMtC,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACgB,GAAG,CAA6C,aAAa,CAAC;MAC9F;MACA,IAAIZ,QAAQ,CAACE,IAAI,IAAI,OAAOF,QAAQ,CAACE,IAAI,KAAK,QAAQ,IAAI,SAAS,IAAIF,QAAQ,CAACE,IAAI,EAAE;QACpF,OAAOF,QAAQ,CAACE,IAAI,CAACqC,OAAO;MAC9B;MACA,OAAOvC,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAEDsC,YAAY,GAAG,MAAOC,EAAU,IAAyB;MACvD,MAAMzC,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACgB,GAAG,CAAY,cAAc6B,EAAE,GAAG,CAAC;MACnE,OAAOzC,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAEDwC,eAAe,GAAG,MAAOxC,IAAwB,IAAyB;MACxE,MAAMF,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACK,IAAI,CAAY,aAAa,EAAEC,IAAI,CAAC;MACpE,OAAOF,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAEDyC,eAAe,GAAG,OAAOF,EAAU,EAAEvC,IAAwB,KAAyB;MACpF,MAAMF,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACkB,GAAG,CAAY,cAAc2B,EAAE,GAAG,EAAEvC,IAAI,CAAC;MACzE,OAAOF,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAED0C,eAAe,GAAG,MAAOH,EAAU,IAAoB;MACrD,MAAM,IAAI,CAAC7C,GAAG,CAACkC,MAAM,CAAC,cAAcW,EAAE,GAAG,CAAC;IAC5C,CAAC;IAED;IAAA,KACAI,SAAS,GAAG,MAAOC,MAIlB,IAAuB;MACtB,MAAM9C,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACgB,GAAG,CAAqC,oBAAoB,EAAE;QAAEkC;MAAO,CAAC,CAAC;MACzG;MACA,IAAI9C,QAAQ,CAACE,IAAI,IAAI,OAAOF,QAAQ,CAACE,IAAI,KAAK,QAAQ,IAAI,SAAS,IAAIF,QAAQ,CAACE,IAAI,EAAE;QACpF,OAAOF,QAAQ,CAACE,IAAI,CAACqC,OAAO;MAC9B;MACA,OAAOvC,QAAQ,CAACE,IAAI;IACtB,CAAC;IAED;IAAA,KACA6C,WAAW,GAAG,MAAOC,WAAmB,IAAqB;MAC3D,MAAMhD,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACgB,GAAG,CAAiC,cAAcoC,WAAW,YAAY,CAAC;MAC1G;MACA,IAAIhD,QAAQ,CAACE,IAAI,IAAI,OAAOF,QAAQ,CAACE,IAAI,KAAK,QAAQ,IAAI,SAAS,IAAIF,QAAQ,CAACE,IAAI,EAAE;QACpF,OAAOF,QAAQ,CAACE,IAAI,CAACqC,OAAO;MAC9B;MACA,OAAOvC,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAED+C,aAAa,GAAG,OAAOD,WAAmB,EAAE9C,IAAS,KAAmB;MACtE,MAAMF,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACK,IAAI,CAAM,cAAc+C,WAAW,YAAY,EAAE9C,IAAI,CAAC;MACtF,OAAOF,QAAQ,CAACE,IAAI;IACtB,CAAC;IAED;IAAA,KACAgD,eAAe,GAAG,MAAOF,WAAmB,IAA6B;MACvE,MAAMhD,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACgB,GAAG,CAAiD,cAAcoC,WAAW,gBAAgB,CAAC;MAC9H;MACA,IAAIhD,QAAQ,CAACE,IAAI,IAAI,OAAOF,QAAQ,CAACE,IAAI,KAAK,QAAQ,IAAI,SAAS,IAAIF,QAAQ,CAACE,IAAI,EAAE;QACpF,OAAOF,QAAQ,CAACE,IAAI,CAACqC,OAAO;MAC9B;MACA,OAAOvC,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAEDiD,iBAAiB,GAAG,OAAOH,WAAmB,EAAE9C,IAA0B,KAA2B;MACnG,MAAMF,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACK,IAAI,CAAc,cAAc+C,WAAW,gBAAgB,EAAE9C,IAAI,CAAC;MAClG,OAAOF,QAAQ,CAACE,IAAI;IACtB,CAAC;IAED;IAAA,KACAkD,qBAAqB,GAAG,MAAOJ,WAAmB,IAAmB;MACnE,MAAMhD,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACgB,GAAG,CAAC,cAAcoC,WAAW,aAAa,CAAC;MAC3E,OAAOhD,QAAQ,CAACE,IAAI;IACtB,CAAC;IAED;IAAA,KACAmD,oBAAoB,GAAG,YAAwC;MAC7D,MAAMrD,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACgB,GAAG,CAAoB,sBAAsB,CAAC;MAC9E,OAAOZ,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAEDoD,iBAAiB,GAAG,MAAOR,MAK1B,IAAsC;MACrC,MAAM9C,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACgB,GAAG,CAAC,mBAAmB,EAAE;QAAEkC;MAAO,CAAC,CAAC;MACpE,OAAO9C,QAAQ,CAACE,IAAI;IACtB,CAAC;IAAA,KAEDqD,YAAY,GAAG,MAAOT,MAMrB,IAAsC;MACrC,MAAM9C,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACgB,GAAG,CAAC,uBAAuB,EAAE;QAAEkC;MAAO,CAAC,CAAC;MACxE,OAAO9C,QAAQ,CAACE,IAAI;IACtB,CAAC;IAED;IAAA,KACAsD,WAAW,GAAG,YAAyC;MACrD,MAAMxD,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAACgB,GAAG,CAAC,eAAe,CAAC;MACpD,OAAOZ,QAAQ,CAACE,IAAI;IACtB,CAAC;IAED;IAAA,KACAuD,OAAO,GAAG,MAAUC,MAA0B,IAAiB;MAC7D,MAAM1D,QAAQ,GAAG,MAAM,IAAI,CAACJ,GAAG,CAAC6D,OAAO,CAAIC,MAAM,CAAC;MAClD,OAAO1D,QAAQ,CAACE,IAAI;IACtB,CAAC;IAhWC,IAAI,CAACL,OAAO,GAAG8D,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,SAAS;IAEzD,IAAI,CAACjE,GAAG,GAAGJ,KAAK,CAACsE,MAAM,CAAC;MACtBjE,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBkE,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IAEF,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC1B;EAEQA,iBAAiBA,CAAA,EAAS;IAChC;IACA,IAAI,CAACrE,GAAG,CAACsE,YAAY,CAACT,OAAO,CAACU,GAAG,CAC9BT,MAAM,IAAK;MACV;MACA,MAAMxC,KAAK,GAAG,IAAI,CAACkD,cAAc,CAAC,CAAC;MACnC,IAAIlD,KAAK,EAAE;QACTwC,MAAM,CAACM,OAAO,CAACK,aAAa,GAAG,UAAUnD,KAAK,EAAE;MAClD;;MAEA;MACAwC,MAAM,CAACM,OAAO,CAAC,kBAAkB,CAAC,GAAG,gBAAgB;MACrDN,MAAM,CAACM,OAAO,CAAC,kBAAkB,CAAC,GAAG,OAAO;;MAE5C;MACA,IAAIL,OAAO,CAACC,GAAG,CAACU,QAAQ,KAAK,aAAa,EAAE;QAAA,IAAAC,cAAA;QAC1CC,OAAO,CAACC,GAAG,CAAC,iBAAAF,cAAA,GAAgBb,MAAM,CAACgB,MAAM,cAAAH,cAAA,uBAAbA,cAAA,CAAeI,WAAW,CAAC,CAAC,IAAIjB,MAAM,CAACkB,GAAG,EAAE,CAAC;MAC3E;MAEA,OAAOlB,MAAM;IACf,CAAC,EACAmB,KAAK,IAAK;MACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;;IAED;IACA,IAAI,CAACjF,GAAG,CAACsE,YAAY,CAAClE,QAAQ,CAACmE,GAAG,CAC/BnE,QAAuB,IAAK;MAC3B,OAAOA,QAAQ;IACjB,CAAC,EACD,MAAO6E,KAAK,IAAK;MAAA,IAAAG,eAAA;MACf,MAAMC,eAAe,GAAGJ,KAAK,CAACnB,MAAM;;MAEpC;MACA,IAAI,EAAAsB,eAAA,GAAAH,KAAK,CAAC7E,QAAQ,cAAAgF,eAAA,uBAAdA,eAAA,CAAgBE,MAAM,MAAK,GAAG,IAAI,CAACD,eAAe,CAACE,MAAM,EAAE;QAC7DF,eAAe,CAACE,MAAM,GAAG,IAAI;QAE7B,IAAI;UACF;UACA,MAAM3E,YAAY,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;UAC3C,IAAID,YAAY,EAAE;YAChB,MAAMR,QAAQ,GAAG,MAAM,IAAI,CAACoF,kBAAkB,CAAC5E,YAAY,CAAC;YAC5D,IAAI,CAACJ,SAAS,CAACJ,QAAQ,CAACE,IAAI,CAACC,YAAY,EAAEK,YAAY,CAAC;;YAExD;YACAyE,eAAe,CAACjB,OAAO,CAACK,aAAa,GAAG,UAAUrE,QAAQ,CAACE,IAAI,CAACC,YAAY,EAAE;YAC9E,OAAO,IAAI,CAACP,GAAG,CAACqF,eAAe,CAAC;UAClC;QACF,CAAC,CAAC,OAAOI,YAAY,EAAE;UACrB;UACA,IAAI,CAAC3E,WAAW,CAAC,CAAC;UAClB4E,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;UAC/B,OAAOV,OAAO,CAACC,MAAM,CAACM,YAAY,CAAC;QACrC;MACF;;MAEA;MACA,IAAI,CAACI,cAAc,CAACZ,KAAK,CAAC;MAC1B,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;EACH;EAEQY,cAAcA,CAACZ,KAAU,EAAQ;IAAA,IAAAa,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA;IACvC,MAAMC,OAAO,GAAG,EAAAH,gBAAA,GAAAb,KAAK,CAAC7E,QAAQ,cAAA0F,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxF,IAAI,cAAAyF,qBAAA,uBAApBA,qBAAA,CAAsBE,OAAO,KAAIhB,KAAK,CAACgB,OAAO,IAAI,mBAAmB;;IAErF;IACA,MAAMC,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;IAC/B,IAAI,CAACA,YAAY,CAACC,QAAQ,EAAAH,gBAAA,GAACf,KAAK,CAAC7E,QAAQ,cAAA4F,gBAAA,uBAAdA,gBAAA,CAAgBV,MAAM,CAAC,EAAE;MAClDzF,KAAK,CAACoF,KAAK,CAACgB,OAAO,CAAC;IACtB;;IAEA;IACA,IAAIlC,OAAO,CAACC,GAAG,CAACU,QAAQ,KAAK,aAAa,EAAE;MAAA,IAAA0B,gBAAA;MAC1CxB,OAAO,CAACK,KAAK,CAAC,YAAY,EAAE,EAAAmB,gBAAA,GAAAnB,KAAK,CAAC7E,QAAQ,cAAAgG,gBAAA,uBAAdA,gBAAA,CAAgB9F,IAAI,KAAI2E,KAAK,CAACgB,OAAO,CAAC;IACpE;EACF;;EAEA;EACQzB,cAAcA,CAAA,EAAkB;IACtC,OAAO6B,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;EAC7C;EAEQzF,eAAeA,CAAA,EAAkB;IACvC,OAAOwF,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;EAC9C;EAEQ9F,SAASA,CAAC+F,WAAmB,EAAE3F,YAAoB,EAAQ;IACjEyF,YAAY,CAACG,OAAO,CAAC,cAAc,EAAED,WAAW,CAAC;IACjDF,YAAY,CAACG,OAAO,CAAC,eAAe,EAAE5F,YAAY,CAAC;EACrD;EAEQE,WAAWA,CAAA,EAAS;IAC1BuF,YAAY,CAACI,UAAU,CAAC,cAAc,CAAC;IACvCJ,YAAY,CAACI,UAAU,CAAC,eAAe,CAAC;EAC1C;EAEA,MAAcjB,kBAAkBA,CAAC5E,YAAoB,EAA0B;IAC7E,OAAO,IAAI,CAACZ,GAAG,CAACK,IAAI,CAAC,sBAAsB,EAAE;MAC3CqG,OAAO,EAAE9F;IACX,CAAC,CAAC;EACJ;AA8OF;;AAEA;AACA,MAAM+F,UAAU,GAAG,IAAI7G,UAAU,CAAC,CAAC;AAEnC,eAAe6G,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}