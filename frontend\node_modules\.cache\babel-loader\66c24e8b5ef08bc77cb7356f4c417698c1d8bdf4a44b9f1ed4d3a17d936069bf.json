{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\TrustVault\\\\frontend\\\\src\\\\pages\\\\Dashboard\\\\DashboardPage.tsx\",\n  _s = $RefreshSig$();\n// TrustVault - Dashboard Page\n\nimport React from 'react';\nimport { Box, Grid, Card, CardContent, Typography, List, ListItem, ListItemText, Chip, IconButton, Tooltip } from '@mui/material';\nimport { TrendingUp, Security, AccountBalance, Warning, Refresh } from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { useQuery } from 'react-query';\n\n// Store\nimport { useAuthStore } from '../../store/authStore';\n\n// Services\nimport apiService from '../../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DashboardPage = () => {\n  _s();\n  const {\n    user\n  } = useAuthStore();\n\n  // Fetch dashboard data\n  const {\n    data: portfolios,\n    isLoading: portfoliosLoading\n  } = useQuery('portfolios', apiService.getPortfolios, {\n    staleTime: 5 * 60 * 1000 // 5 minutes\n  });\n  const {\n    data: securityDashboard,\n    isLoading: securityLoading\n  } = useQuery('security-dashboard', apiService.getSecurityDashboard, {\n    staleTime: 2 * 60 * 1000 // 2 minutes\n  });\n  const totalPortfolioValue = Array.isArray(portfolios) ? portfolios.reduce((sum, portfolio) => sum + parseFloat(portfolio.total_value), 0) : 0;\n  const formatCurrency = value => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(value);\n  };\n  const getSecurityStatusColor = unresolved => {\n    if (unresolved === 0) return 'success';\n    if (unresolved <= 5) return 'warning';\n    return 'error';\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"Dashboard - TrustVault\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: \"Your secure portfolio dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        mb: 4,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h1\",\n          gutterBottom: true,\n          children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.first_name, \"!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          color: \"text.secondary\",\n          children: \"Here's an overview of your portfolio and security status.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        mb: 4,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    color: \"text.secondary\",\n                    gutterBottom: true,\n                    children: \"Total Portfolio Value\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 97,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h5\",\n                    component: \"div\",\n                    children: formatCurrency(totalPortfolioValue)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 100,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 96,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TrendingUp, {\n                  color: \"primary\",\n                  sx: {\n                    fontSize: 40\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 104,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    color: \"text.secondary\",\n                    gutterBottom: true,\n                    children: \"Active Portfolios\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 116,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h5\",\n                    component: \"div\",\n                    children: (portfolios === null || portfolios === void 0 ? void 0 : portfolios.length) || 0\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 119,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 115,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(AccountBalance, {\n                  color: \"primary\",\n                  sx: {\n                    fontSize: 40\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    color: \"text.secondary\",\n                    gutterBottom: true,\n                    children: \"Security Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 135,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    label: (securityDashboard === null || securityDashboard === void 0 ? void 0 : securityDashboard.security_events.unresolved) === 0 ? 'Secure' : `${securityDashboard === null || securityDashboard === void 0 ? void 0 : securityDashboard.security_events.unresolved} Issues`,\n                    color: getSecurityStatusColor((securityDashboard === null || securityDashboard === void 0 ? void 0 : securityDashboard.security_events.unresolved) || 0),\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 138,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Security, {\n                  color: \"primary\",\n                  sx: {\n                    fontSize: 40\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    color: \"text.secondary\",\n                    gutterBottom: true,\n                    children: \"Login Attempts (24h)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 162,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h5\",\n                    component: \"div\",\n                    children: (securityDashboard === null || securityDashboard === void 0 ? void 0 : securityDashboard.login_attempts.successful_last_24h) || 0\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 165,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Refresh\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    children: /*#__PURE__*/_jsxDEV(Refresh, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 171,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 170,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                mb: 2,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  component: \"h2\",\n                  children: \"Portfolio Overview\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Refresh\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    children: /*#__PURE__*/_jsxDEV(Refresh, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 192,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 191,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this), portfoliosLoading ? /*#__PURE__*/_jsxDEV(Typography, {\n                children: \"Loading portfolios...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 19\n              }, this) : portfolios && portfolios.length > 0 ? /*#__PURE__*/_jsxDEV(List, {\n                children: portfolios.slice(0, 5).map(portfolio => /*#__PURE__*/_jsxDEV(ListItem, {\n                  divider: true,\n                  children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: portfolio.name,\n                    secondary: `${portfolio.portfolio_type} • ${portfolio.holdings_count || 0} holdings`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    textAlign: \"right\",\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      children: formatCurrency(parseFloat(portfolio.total_value))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 208,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: portfolio.currency\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 211,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 25\n                  }, this)]\n                }, portfolio.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(Box, {\n                textAlign: \"center\",\n                py: 4,\n                children: [/*#__PURE__*/_jsxDEV(AccountBalance, {\n                  sx: {\n                    fontSize: 64,\n                    color: 'text.secondary',\n                    mb: 2\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"text.secondary\",\n                  gutterBottom: true,\n                  children: \"No Portfolios Yet\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Create your first portfolio to get started\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                mb: 2,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  component: \"h2\",\n                  children: \"Security Events\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Refresh\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    children: /*#__PURE__*/_jsxDEV(Refresh, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 243,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 17\n              }, this), securityLoading ? /*#__PURE__*/_jsxDEV(Typography, {\n                children: \"Loading security data...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 19\n              }, this) : securityDashboard ? /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  mb: 2,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: \"Last 24 hours\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    children: securityDashboard.security_events.last_24h\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(List, {\n                  dense: true,\n                  children: securityDashboard.recent_events.slice(0, 3).map(event => /*#__PURE__*/_jsxDEV(ListItem, {\n                    sx: {\n                      px: 0\n                    },\n                    children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                      primary: /*#__PURE__*/_jsxDEV(Box, {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: 1,\n                        children: [/*#__PURE__*/_jsxDEV(Warning, {\n                          color: event.risk_level === 'CRITICAL' ? 'error' : event.risk_level === 'HIGH' ? 'warning' : 'info',\n                          fontSize: \"small\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 267,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          children: event.event_type.replace('_', ' ')\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 277,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 266,\n                        columnNumber: 31\n                      }, this),\n                      secondary: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: new Date(event.created_at).toLocaleString()\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 283,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 264,\n                      columnNumber: 27\n                    }, this)\n                  }, event.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 263,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 21\n                }, this), securityDashboard.security_events.unresolved > 0 && /*#__PURE__*/_jsxDEV(Box, {\n                  mt: 2,\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: `${securityDashboard.security_events.unresolved} Unresolved`,\n                    color: \"warning\",\n                    size: \"small\",\n                    icon: /*#__PURE__*/_jsxDEV(Warning, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 298,\n                      columnNumber: 33\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 294,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n                color: \"text.secondary\",\n                children: \"No security data available\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(DashboardPage, \"0lrm4VrF/ynCMVU20LMbLMpu1nc=\", false, function () {\n  return [useAuthStore, useQuery, useQuery];\n});\n_c = DashboardPage;\nexport default DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");", "map": {"version": 3, "names": ["React", "Box", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "List", "ListItem", "ListItemText", "Chip", "IconButton", "<PERSON><PERSON><PERSON>", "TrendingUp", "Security", "AccountBalance", "Warning", "Refresh", "<PERSON><PERSON><PERSON>", "useQuery", "useAuthStore", "apiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DashboardPage", "_s", "user", "data", "portfolios", "isLoading", "portfoliosLoading", "getPortfolios", "staleTime", "securityDashboard", "securityLoading", "getSecurityDashboard", "totalPortfolioValue", "Array", "isArray", "reduce", "sum", "portfolio", "parseFloat", "total_value", "formatCurrency", "value", "Intl", "NumberFormat", "style", "currency", "format", "getSecurityStatusColor", "unresolved", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "content", "mb", "variant", "component", "gutterBottom", "first_name", "color", "container", "spacing", "item", "xs", "sm", "md", "display", "alignItems", "justifyContent", "sx", "fontSize", "length", "label", "security_events", "size", "login_attempts", "successful_last_24h", "title", "slice", "map", "divider", "primary", "secondary", "portfolio_type", "holdings_count", "textAlign", "id", "py", "last_24h", "dense", "recent_events", "event", "px", "gap", "risk_level", "event_type", "replace", "Date", "created_at", "toLocaleString", "mt", "icon", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/TrustVault/frontend/src/pages/Dashboard/DashboardPage.tsx"], "sourcesContent": ["// TrustVault - Dashboard Page\n\nimport React from 'react';\nimport {\n  Box,\n  Grid,\n  Card,\n  CardContent,\n  Typography,\n  Paper,\n  List,\n  ListItem,\n  ListItemText,\n  Chip,\n  IconButton,\n  Tooltip,\n} from '@mui/material';\nimport {\n  TrendingUp,\n  Security,\n  AccountBalance,\n  Warning,\n  Refresh,\n} from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { useQuery } from 'react-query';\n\n// Store\nimport { useAuthStore } from '../../store/authStore';\n\n// Services\nimport apiService from '../../services/api';\n\nconst DashboardPage: React.FC = () => {\n  const { user } = useAuthStore();\n\n  // Fetch dashboard data\n  const { data: portfolios, isLoading: portfoliosLoading } = useQuery(\n    'portfolios',\n    apiService.getPortfolios,\n    {\n      staleTime: 5 * 60 * 1000, // 5 minutes\n    }\n  );\n\n  const { data: securityDashboard, isLoading: securityLoading } = useQuery(\n    'security-dashboard',\n    apiService.getSecurityDashboard,\n    {\n      staleTime: 2 * 60 * 1000, // 2 minutes\n    }\n  );\n\n  const totalPortfolioValue = Array.isArray(portfolios)\n    ? portfolios.reduce((sum, portfolio) => sum + parseFloat(portfolio.total_value), 0)\n    : 0;\n\n  const formatCurrency = (value: number) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n    }).format(value);\n  };\n\n  const getSecurityStatusColor = (unresolved: number) => {\n    if (unresolved === 0) return 'success';\n    if (unresolved <= 5) return 'warning';\n    return 'error';\n  };\n\n  return (\n    <>\n      <Helmet>\n        <title>Dashboard - TrustVault</title>\n        <meta name=\"description\" content=\"Your secure portfolio dashboard\" />\n      </Helmet>\n\n      <Box>\n        {/* Welcome Section */}\n        <Box mb={4}>\n          <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n            Welcome back, {user?.first_name}!\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            Here's an overview of your portfolio and security status.\n          </Typography>\n        </Box>\n\n        {/* Key Metrics */}\n        <Grid container spacing={3} mb={4}>\n          {/* Total Portfolio Value */}\n          <Grid item xs={12} sm={6} md={3}>\n            <Card>\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n                  <Box>\n                    <Typography color=\"text.secondary\" gutterBottom>\n                      Total Portfolio Value\n                    </Typography>\n                    <Typography variant=\"h5\" component=\"div\">\n                      {formatCurrency(totalPortfolioValue)}\n                    </Typography>\n                  </Box>\n                  <TrendingUp color=\"primary\" sx={{ fontSize: 40 }} />\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n\n          {/* Number of Portfolios */}\n          <Grid item xs={12} sm={6} md={3}>\n            <Card>\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n                  <Box>\n                    <Typography color=\"text.secondary\" gutterBottom>\n                      Active Portfolios\n                    </Typography>\n                    <Typography variant=\"h5\" component=\"div\">\n                      {portfolios?.length || 0}\n                    </Typography>\n                  </Box>\n                  <AccountBalance color=\"primary\" sx={{ fontSize: 40 }} />\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n\n          {/* Security Status */}\n          <Grid item xs={12} sm={6} md={3}>\n            <Card>\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n                  <Box>\n                    <Typography color=\"text.secondary\" gutterBottom>\n                      Security Status\n                    </Typography>\n                    <Chip\n                      label={\n                        securityDashboard?.security_events.unresolved === 0\n                          ? 'Secure'\n                          : `${securityDashboard?.security_events.unresolved} Issues`\n                      }\n                      color={getSecurityStatusColor(\n                        securityDashboard?.security_events.unresolved || 0\n                      )}\n                      size=\"small\"\n                    />\n                  </Box>\n                  <Security color=\"primary\" sx={{ fontSize: 40 }} />\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n\n          {/* Recent Activity */}\n          <Grid item xs={12} sm={6} md={3}>\n            <Card>\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n                  <Box>\n                    <Typography color=\"text.secondary\" gutterBottom>\n                      Login Attempts (24h)\n                    </Typography>\n                    <Typography variant=\"h5\" component=\"div\">\n                      {securityDashboard?.login_attempts.successful_last_24h || 0}\n                    </Typography>\n                  </Box>\n                  <Tooltip title=\"Refresh\">\n                    <IconButton size=\"small\">\n                      <Refresh />\n                    </IconButton>\n                  </Tooltip>\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n        </Grid>\n\n        {/* Content Grid */}\n        <Grid container spacing={3}>\n          {/* Portfolio Overview */}\n          <Grid item xs={12} md={8}>\n            <Card>\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\" mb={2}>\n                  <Typography variant=\"h6\" component=\"h2\">\n                    Portfolio Overview\n                  </Typography>\n                  <Tooltip title=\"Refresh\">\n                    <IconButton size=\"small\">\n                      <Refresh />\n                    </IconButton>\n                  </Tooltip>\n                </Box>\n\n                {portfoliosLoading ? (\n                  <Typography>Loading portfolios...</Typography>\n                ) : portfolios && portfolios.length > 0 ? (\n                  <List>\n                    {portfolios.slice(0, 5).map((portfolio) => (\n                      <ListItem key={portfolio.id} divider>\n                        <ListItemText\n                          primary={portfolio.name}\n                          secondary={`${portfolio.portfolio_type} • ${portfolio.holdings_count || 0} holdings`}\n                        />\n                        <Box textAlign=\"right\">\n                          <Typography variant=\"h6\">\n                            {formatCurrency(parseFloat(portfolio.total_value))}\n                          </Typography>\n                          <Typography variant=\"body2\" color=\"text.secondary\">\n                            {portfolio.currency}\n                          </Typography>\n                        </Box>\n                      </ListItem>\n                    ))}\n                  </List>\n                ) : (\n                  <Box textAlign=\"center\" py={4}>\n                    <AccountBalance sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />\n                    <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\n                      No Portfolios Yet\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Create your first portfolio to get started\n                    </Typography>\n                  </Box>\n                )}\n              </CardContent>\n            </Card>\n          </Grid>\n\n          {/* Security Events */}\n          <Grid item xs={12} md={4}>\n            <Card>\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\" mb={2}>\n                  <Typography variant=\"h6\" component=\"h2\">\n                    Security Events\n                  </Typography>\n                  <Tooltip title=\"Refresh\">\n                    <IconButton size=\"small\">\n                      <Refresh />\n                    </IconButton>\n                  </Tooltip>\n                </Box>\n\n                {securityLoading ? (\n                  <Typography>Loading security data...</Typography>\n                ) : securityDashboard ? (\n                  <Box>\n                    <Box mb={2}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Last 24 hours\n                      </Typography>\n                      <Typography variant=\"h4\">\n                        {securityDashboard.security_events.last_24h}\n                      </Typography>\n                    </Box>\n\n                    <List dense>\n                      {securityDashboard.recent_events.slice(0, 3).map((event) => (\n                        <ListItem key={event.id} sx={{ px: 0 }}>\n                          <ListItemText\n                            primary={\n                              <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                                <Warning\n                                  color={\n                                    event.risk_level === 'CRITICAL'\n                                      ? 'error'\n                                      : event.risk_level === 'HIGH'\n                                      ? 'warning'\n                                      : 'info'\n                                  }\n                                  fontSize=\"small\"\n                                />\n                                <Typography variant=\"body2\">\n                                  {event.event_type.replace('_', ' ')}\n                                </Typography>\n                              </Box>\n                            }\n                            secondary={\n                              <Typography variant=\"caption\" color=\"text.secondary\">\n                                {new Date(event.created_at).toLocaleString()}\n                              </Typography>\n                            }\n                          />\n                        </ListItem>\n                      ))}\n                    </List>\n\n                    {securityDashboard.security_events.unresolved > 0 && (\n                      <Box mt={2}>\n                        <Chip\n                          label={`${securityDashboard.security_events.unresolved} Unresolved`}\n                          color=\"warning\"\n                          size=\"small\"\n                          icon={<Warning />}\n                        />\n                      </Box>\n                    )}\n                  </Box>\n                ) : (\n                  <Typography color=\"text.secondary\">\n                    No security data available\n                  </Typography>\n                )}\n              </CardContent>\n            </Card>\n          </Grid>\n        </Grid>\n      </Box>\n    </>\n  );\n};\n\nexport default DashboardPage;\n"], "mappings": ";;AAAA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EAEVC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,IAAI,EACJC,UAAU,EACVC,OAAO,QACF,eAAe;AACtB,SACEC,UAAU,EACVC,QAAQ,EACRC,cAAc,EACdC,OAAO,EACPC,OAAO,QACF,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,QAAQ,QAAQ,aAAa;;AAEtC;AACA,SAASC,YAAY,QAAQ,uBAAuB;;AAEpD;AACA,OAAOC,UAAU,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5C,MAAMC,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAM;IAAEC;EAAK,CAAC,GAAGR,YAAY,CAAC,CAAC;;EAE/B;EACA,MAAM;IAAES,IAAI,EAAEC,UAAU;IAAEC,SAAS,EAAEC;EAAkB,CAAC,GAAGb,QAAQ,CACjE,YAAY,EACZE,UAAU,CAACY,aAAa,EACxB;IACEC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAE;EAC5B,CACF,CAAC;EAED,MAAM;IAAEL,IAAI,EAAEM,iBAAiB;IAAEJ,SAAS,EAAEK;EAAgB,CAAC,GAAGjB,QAAQ,CACtE,oBAAoB,EACpBE,UAAU,CAACgB,oBAAoB,EAC/B;IACEH,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAE;EAC5B,CACF,CAAC;EAED,MAAMI,mBAAmB,GAAGC,KAAK,CAACC,OAAO,CAACV,UAAU,CAAC,GACjDA,UAAU,CAACW,MAAM,CAAC,CAACC,GAAG,EAAEC,SAAS,KAAKD,GAAG,GAAGE,UAAU,CAACD,SAAS,CAACE,WAAW,CAAC,EAAE,CAAC,CAAC,GACjF,CAAC;EAEL,MAAMC,cAAc,GAAIC,KAAa,IAAK;IACxC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,KAAK,CAAC;EAClB,CAAC;EAED,MAAMM,sBAAsB,GAAIC,UAAkB,IAAK;IACrD,IAAIA,UAAU,KAAK,CAAC,EAAE,OAAO,SAAS;IACtC,IAAIA,UAAU,IAAI,CAAC,EAAE,OAAO,SAAS;IACrC,OAAO,OAAO;EAChB,CAAC;EAED,oBACE/B,OAAA,CAAAE,SAAA;IAAA8B,QAAA,gBACEhC,OAAA,CAACL,MAAM;MAAAqC,QAAA,gBACLhC,OAAA;QAAAgC,QAAA,EAAO;MAAsB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACrCpC,OAAA;QAAMqC,IAAI,EAAC,aAAa;QAACC,OAAO,EAAC;MAAiC;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CAAC,eAETpC,OAAA,CAACrB,GAAG;MAAAqD,QAAA,gBAEFhC,OAAA,CAACrB,GAAG;QAAC4D,EAAE,EAAE,CAAE;QAAAP,QAAA,gBACThC,OAAA,CAACjB,UAAU;UAACyD,OAAO,EAAC,IAAI;UAACC,SAAS,EAAC,IAAI;UAACC,YAAY;UAAAV,QAAA,GAAC,gBACrC,EAAC3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsC,UAAU,EAAC,GAClC;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbpC,OAAA,CAACjB,UAAU;UAACyD,OAAO,EAAC,OAAO;UAACI,KAAK,EAAC,gBAAgB;UAAAZ,QAAA,EAAC;QAEnD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNpC,OAAA,CAACpB,IAAI;QAACiE,SAAS;QAACC,OAAO,EAAE,CAAE;QAACP,EAAE,EAAE,CAAE;QAAAP,QAAA,gBAEhChC,OAAA,CAACpB,IAAI;UAACmE,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAlB,QAAA,eAC9BhC,OAAA,CAACnB,IAAI;YAAAmD,QAAA,eACHhC,OAAA,CAAClB,WAAW;cAAAkD,QAAA,eACVhC,OAAA,CAACrB,GAAG;gBAACwE,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,cAAc,EAAC,eAAe;gBAAArB,QAAA,gBACpEhC,OAAA,CAACrB,GAAG;kBAAAqD,QAAA,gBACFhC,OAAA,CAACjB,UAAU;oBAAC6D,KAAK,EAAC,gBAAgB;oBAACF,YAAY;oBAAAV,QAAA,EAAC;kBAEhD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbpC,OAAA,CAACjB,UAAU;oBAACyD,OAAO,EAAC,IAAI;oBAACC,SAAS,EAAC,KAAK;oBAAAT,QAAA,EACrCT,cAAc,CAACR,mBAAmB;kBAAC;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNpC,OAAA,CAACV,UAAU;kBAACsD,KAAK,EAAC,SAAS;kBAACU,EAAE,EAAE;oBAAEC,QAAQ,EAAE;kBAAG;gBAAE;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGPpC,OAAA,CAACpB,IAAI;UAACmE,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAlB,QAAA,eAC9BhC,OAAA,CAACnB,IAAI;YAAAmD,QAAA,eACHhC,OAAA,CAAClB,WAAW;cAAAkD,QAAA,eACVhC,OAAA,CAACrB,GAAG;gBAACwE,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,cAAc,EAAC,eAAe;gBAAArB,QAAA,gBACpEhC,OAAA,CAACrB,GAAG;kBAAAqD,QAAA,gBACFhC,OAAA,CAACjB,UAAU;oBAAC6D,KAAK,EAAC,gBAAgB;oBAACF,YAAY;oBAAAV,QAAA,EAAC;kBAEhD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbpC,OAAA,CAACjB,UAAU;oBAACyD,OAAO,EAAC,IAAI;oBAACC,SAAS,EAAC,KAAK;oBAAAT,QAAA,EACrC,CAAAzB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEiD,MAAM,KAAI;kBAAC;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNpC,OAAA,CAACR,cAAc;kBAACoD,KAAK,EAAC,SAAS;kBAACU,EAAE,EAAE;oBAAEC,QAAQ,EAAE;kBAAG;gBAAE;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGPpC,OAAA,CAACpB,IAAI;UAACmE,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAlB,QAAA,eAC9BhC,OAAA,CAACnB,IAAI;YAAAmD,QAAA,eACHhC,OAAA,CAAClB,WAAW;cAAAkD,QAAA,eACVhC,OAAA,CAACrB,GAAG;gBAACwE,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,cAAc,EAAC,eAAe;gBAAArB,QAAA,gBACpEhC,OAAA,CAACrB,GAAG;kBAAAqD,QAAA,gBACFhC,OAAA,CAACjB,UAAU;oBAAC6D,KAAK,EAAC,gBAAgB;oBAACF,YAAY;oBAAAV,QAAA,EAAC;kBAEhD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbpC,OAAA,CAACb,IAAI;oBACHsE,KAAK,EACH,CAAA7C,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE8C,eAAe,CAAC3B,UAAU,MAAK,CAAC,GAC/C,QAAQ,GACR,GAAGnB,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE8C,eAAe,CAAC3B,UAAU,SACrD;oBACDa,KAAK,EAAEd,sBAAsB,CAC3B,CAAAlB,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE8C,eAAe,CAAC3B,UAAU,KAAI,CACnD,CAAE;oBACF4B,IAAI,EAAC;kBAAO;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNpC,OAAA,CAACT,QAAQ;kBAACqD,KAAK,EAAC,SAAS;kBAACU,EAAE,EAAE;oBAAEC,QAAQ,EAAE;kBAAG;gBAAE;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGPpC,OAAA,CAACpB,IAAI;UAACmE,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAlB,QAAA,eAC9BhC,OAAA,CAACnB,IAAI;YAAAmD,QAAA,eACHhC,OAAA,CAAClB,WAAW;cAAAkD,QAAA,eACVhC,OAAA,CAACrB,GAAG;gBAACwE,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,cAAc,EAAC,eAAe;gBAAArB,QAAA,gBACpEhC,OAAA,CAACrB,GAAG;kBAAAqD,QAAA,gBACFhC,OAAA,CAACjB,UAAU;oBAAC6D,KAAK,EAAC,gBAAgB;oBAACF,YAAY;oBAAAV,QAAA,EAAC;kBAEhD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbpC,OAAA,CAACjB,UAAU;oBAACyD,OAAO,EAAC,IAAI;oBAACC,SAAS,EAAC,KAAK;oBAAAT,QAAA,EACrC,CAAApB,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEgD,cAAc,CAACC,mBAAmB,KAAI;kBAAC;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNpC,OAAA,CAACX,OAAO;kBAACyE,KAAK,EAAC,SAAS;kBAAA9B,QAAA,eACtBhC,OAAA,CAACZ,UAAU;oBAACuE,IAAI,EAAC,OAAO;oBAAA3B,QAAA,eACtBhC,OAAA,CAACN,OAAO;sBAAAuC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPpC,OAAA,CAACpB,IAAI;QAACiE,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAd,QAAA,gBAEzBhC,OAAA,CAACpB,IAAI;UAACmE,IAAI;UAACC,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAAAlB,QAAA,eACvBhC,OAAA,CAACnB,IAAI;YAAAmD,QAAA,eACHhC,OAAA,CAAClB,WAAW;cAAAkD,QAAA,gBACVhC,OAAA,CAACrB,GAAG;gBAACwE,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,cAAc,EAAC,eAAe;gBAACd,EAAE,EAAE,CAAE;gBAAAP,QAAA,gBAC3EhC,OAAA,CAACjB,UAAU;kBAACyD,OAAO,EAAC,IAAI;kBAACC,SAAS,EAAC,IAAI;kBAAAT,QAAA,EAAC;gBAExC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbpC,OAAA,CAACX,OAAO;kBAACyE,KAAK,EAAC,SAAS;kBAAA9B,QAAA,eACtBhC,OAAA,CAACZ,UAAU;oBAACuE,IAAI,EAAC,OAAO;oBAAA3B,QAAA,eACtBhC,OAAA,CAACN,OAAO;sBAAAuC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,EAEL3B,iBAAiB,gBAChBT,OAAA,CAACjB,UAAU;gBAAAiD,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,GAC5C7B,UAAU,IAAIA,UAAU,CAACiD,MAAM,GAAG,CAAC,gBACrCxD,OAAA,CAAChB,IAAI;gBAAAgD,QAAA,EACFzB,UAAU,CAACwD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAE5C,SAAS,iBACpCpB,OAAA,CAACf,QAAQ;kBAAoBgF,OAAO;kBAAAjC,QAAA,gBAClChC,OAAA,CAACd,YAAY;oBACXgF,OAAO,EAAE9C,SAAS,CAACiB,IAAK;oBACxB8B,SAAS,EAAE,GAAG/C,SAAS,CAACgD,cAAc,MAAMhD,SAAS,CAACiD,cAAc,IAAI,CAAC;kBAAY;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtF,CAAC,eACFpC,OAAA,CAACrB,GAAG;oBAAC2F,SAAS,EAAC,OAAO;oBAAAtC,QAAA,gBACpBhC,OAAA,CAACjB,UAAU;sBAACyD,OAAO,EAAC,IAAI;sBAAAR,QAAA,EACrBT,cAAc,CAACF,UAAU,CAACD,SAAS,CAACE,WAAW,CAAC;oBAAC;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CAAC,eACbpC,OAAA,CAACjB,UAAU;sBAACyD,OAAO,EAAC,OAAO;sBAACI,KAAK,EAAC,gBAAgB;sBAAAZ,QAAA,EAC/CZ,SAAS,CAACQ;oBAAQ;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA,GAZOhB,SAAS,CAACmD,EAAE;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAajB,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,gBAEPpC,OAAA,CAACrB,GAAG;gBAAC2F,SAAS,EAAC,QAAQ;gBAACE,EAAE,EAAE,CAAE;gBAAAxC,QAAA,gBAC5BhC,OAAA,CAACR,cAAc;kBAAC8D,EAAE,EAAE;oBAAEC,QAAQ,EAAE,EAAE;oBAAEX,KAAK,EAAE,gBAAgB;oBAAEL,EAAE,EAAE;kBAAE;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxEpC,OAAA,CAACjB,UAAU;kBAACyD,OAAO,EAAC,IAAI;kBAACI,KAAK,EAAC,gBAAgB;kBAACF,YAAY;kBAAAV,QAAA,EAAC;gBAE7D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbpC,OAAA,CAACjB,UAAU;kBAACyD,OAAO,EAAC,OAAO;kBAACI,KAAK,EAAC,gBAAgB;kBAAAZ,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGPpC,OAAA,CAACpB,IAAI;UAACmE,IAAI;UAACC,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAAAlB,QAAA,eACvBhC,OAAA,CAACnB,IAAI;YAAAmD,QAAA,eACHhC,OAAA,CAAClB,WAAW;cAAAkD,QAAA,gBACVhC,OAAA,CAACrB,GAAG;gBAACwE,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,cAAc,EAAC,eAAe;gBAACd,EAAE,EAAE,CAAE;gBAAAP,QAAA,gBAC3EhC,OAAA,CAACjB,UAAU;kBAACyD,OAAO,EAAC,IAAI;kBAACC,SAAS,EAAC,IAAI;kBAAAT,QAAA,EAAC;gBAExC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbpC,OAAA,CAACX,OAAO;kBAACyE,KAAK,EAAC,SAAS;kBAAA9B,QAAA,eACtBhC,OAAA,CAACZ,UAAU;oBAACuE,IAAI,EAAC,OAAO;oBAAA3B,QAAA,eACtBhC,OAAA,CAACN,OAAO;sBAAAuC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,EAELvB,eAAe,gBACdb,OAAA,CAACjB,UAAU;gBAAAiD,QAAA,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,GAC/CxB,iBAAiB,gBACnBZ,OAAA,CAACrB,GAAG;gBAAAqD,QAAA,gBACFhC,OAAA,CAACrB,GAAG;kBAAC4D,EAAE,EAAE,CAAE;kBAAAP,QAAA,gBACThC,OAAA,CAACjB,UAAU;oBAACyD,OAAO,EAAC,OAAO;oBAACI,KAAK,EAAC,gBAAgB;oBAAAZ,QAAA,EAAC;kBAEnD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbpC,OAAA,CAACjB,UAAU;oBAACyD,OAAO,EAAC,IAAI;oBAAAR,QAAA,EACrBpB,iBAAiB,CAAC8C,eAAe,CAACe;kBAAQ;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAENpC,OAAA,CAAChB,IAAI;kBAAC0F,KAAK;kBAAA1C,QAAA,EACRpB,iBAAiB,CAAC+D,aAAa,CAACZ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAEY,KAAK,iBACrD5E,OAAA,CAACf,QAAQ;oBAAgBqE,EAAE,EAAE;sBAAEuB,EAAE,EAAE;oBAAE,CAAE;oBAAA7C,QAAA,eACrChC,OAAA,CAACd,YAAY;sBACXgF,OAAO,eACLlE,OAAA,CAACrB,GAAG;wBAACwE,OAAO,EAAC,MAAM;wBAACC,UAAU,EAAC,QAAQ;wBAAC0B,GAAG,EAAE,CAAE;wBAAA9C,QAAA,gBAC7ChC,OAAA,CAACP,OAAO;0BACNmD,KAAK,EACHgC,KAAK,CAACG,UAAU,KAAK,UAAU,GAC3B,OAAO,GACPH,KAAK,CAACG,UAAU,KAAK,MAAM,GAC3B,SAAS,GACT,MACL;0BACDxB,QAAQ,EAAC;wBAAO;0BAAAtB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjB,CAAC,eACFpC,OAAA,CAACjB,UAAU;0BAACyD,OAAO,EAAC,OAAO;0BAAAR,QAAA,EACxB4C,KAAK,CAACI,UAAU,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG;wBAAC;0BAAAhD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CACN;sBACD+B,SAAS,eACPnE,OAAA,CAACjB,UAAU;wBAACyD,OAAO,EAAC,SAAS;wBAACI,KAAK,EAAC,gBAAgB;wBAAAZ,QAAA,EACjD,IAAIkD,IAAI,CAACN,KAAK,CAACO,UAAU,CAAC,CAACC,cAAc,CAAC;sBAAC;wBAAAnD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC;oBACb;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC,GAxBWwC,KAAK,CAACL,EAAE;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAyBb,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,EAENxB,iBAAiB,CAAC8C,eAAe,CAAC3B,UAAU,GAAG,CAAC,iBAC/C/B,OAAA,CAACrB,GAAG;kBAAC0G,EAAE,EAAE,CAAE;kBAAArD,QAAA,eACThC,OAAA,CAACb,IAAI;oBACHsE,KAAK,EAAE,GAAG7C,iBAAiB,CAAC8C,eAAe,CAAC3B,UAAU,aAAc;oBACpEa,KAAK,EAAC,SAAS;oBACfe,IAAI,EAAC,OAAO;oBACZ2B,IAAI,eAAEtF,OAAA,CAACP,OAAO;sBAAAwC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,gBAENpC,OAAA,CAACjB,UAAU;gBAAC6D,KAAK,EAAC,gBAAgB;gBAAAZ,QAAA,EAAC;cAEnC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAAChC,EAAA,CAzRID,aAAuB;EAAA,QACVN,YAAY,EAG8BD,QAAQ,EAQHA,QAAQ;AAAA;AAAA2F,EAAA,GAZpEpF,aAAuB;AA2R7B,eAAeA,aAAa;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}