{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\TrustVault\\\\frontend\\\\src\\\\pages\\\\Settings\\\\SettingsPage.tsx\",\n  _s = $RefreshSig$();\n// TrustVault - Settings Page\n\nimport React, { useState } from 'react';\nimport { Box, Typography, Grid, Card, CardContent, FormControl, InputLabel, Select, MenuItem, Switch, Button, Alert, List, ListItem, ListItemText, ListItemSecondaryAction, Dialog, DialogTitle, DialogContent, DialogActions, TextField, CircularProgress, Chip, Divider } from '@mui/material';\nimport { Settings, Notifications, Security, Delete, Save, Download, Devices, Key, Brightness4, Brightness7, BrightnessAuto } from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { toast } from 'react-hot-toast';\nimport { useMutation, useQueryClient, useQuery } from 'react-query';\n\n// Store\nimport { useAuthStore } from '../../store/authStore';\n\n// Contexts\nimport { useTheme } from '../../contexts/ThemeContext';\n\n// Services\nimport apiService from '../../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SettingsPage = () => {\n  _s();\n  const {\n    user\n  } = useAuthStore();\n  const queryClient = useQueryClient();\n  const {\n    themeMode,\n    setThemeMode,\n    isDarkMode\n  } = useTheme();\n  const [settings, setSettings] = useState({\n    timezone: (user === null || user === void 0 ? void 0 : user.timezone) || 'UTC',\n    language: (user === null || user === void 0 ? void 0 : user.language) || 'en',\n    theme: themeMode,\n    notifications: {\n      email: true,\n      push: true,\n      security: true,\n      portfolio: true,\n      marketing: false,\n      news: true,\n      email_frequency: 'daily',\n      push_enabled: true\n    },\n    privacy: {\n      profile_visibility: 'private',\n      portfolio_visibility: 'private',\n      data_sharing: false,\n      analytics: true\n    }\n  });\n  const [deleteAccountDialog, setDeleteAccountDialog] = useState(false);\n  const [deleteConfirmation, setDeleteConfirmation] = useState('');\n  const [sessionsDialog, setSessionsDialog] = useState(false);\n  const [exportingData, setExportingData] = useState(false);\n  const [apiKeysDialog, setApiKeysDialog] = useState(false);\n  const [createApiKeyDialog, setCreateApiKeyDialog] = useState(false);\n  const [newApiKeyName, setNewApiKeyName] = useState('');\n  const [newApiKeyPermissions, setNewApiKeyPermissions] = useState([]);\n\n  // Load user settings\n  const {\n    data: userSettings,\n    isLoading: settingsLoading\n  } = useQuery('user-settings', apiService.getUserSettings, {\n    onSuccess: data => {\n      setSettings(prev => ({\n        ...prev,\n        ...data\n      }));\n    },\n    onError: () => {\n      // Use default settings if API fails\n    }\n  });\n\n  // Load active sessions\n  const {\n    data: activeSessions,\n    refetch: refetchSessions\n  } = useQuery('active-sessions', apiService.getActiveSessions, {\n    enabled: sessionsDialog,\n    onError: () => {\n      toast.error('Failed to load active sessions');\n    }\n  });\n\n  // Load API keys\n  const {\n    data: apiKeys,\n    refetch: refetchApiKeys\n  } = useQuery('api-keys', apiService.getApiKeys, {\n    enabled: apiKeysDialog,\n    onError: () => {\n      toast.error('Failed to load API keys');\n    }\n  });\n  const updateSettingsMutation = useMutation(data => apiService.updateUserSettings(data), {\n    onSuccess: () => {\n      toast.success('Settings updated successfully');\n      queryClient.invalidateQueries(['user-settings', 'user-profile']);\n    },\n    onError: () => {\n      toast.error('Failed to update settings');\n    }\n  });\n  const deleteAccountMutation = useMutation(confirmation => apiService.deleteAccount(confirmation), {\n    onSuccess: () => {\n      toast.success('Account deleted successfully');\n      // Redirect to login or home page\n      window.location.href = '/';\n    },\n    onError: error => {\n      var _error$response, _error$response$data;\n      const message = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to delete account';\n      toast.error(message);\n    }\n  });\n  const revokeSessionMutation = useMutation(sessionId => apiService.revokeSession(sessionId), {\n    onSuccess: () => {\n      toast.success('Session revoked successfully');\n      refetchSessions();\n    },\n    onError: () => {\n      toast.error('Failed to revoke session');\n    }\n  });\n  const revokeAllSessionsMutation = useMutation(() => apiService.revokeAllSessions(), {\n    onSuccess: () => {\n      toast.success('All sessions revoked successfully');\n      refetchSessions();\n    },\n    onError: () => {\n      toast.error('Failed to revoke all sessions');\n    }\n  });\n  const createApiKeyMutation = useMutation(({\n    name,\n    permissions\n  }) => apiService.createApiKey(name, permissions), {\n    onSuccess: () => {\n      toast.success('API key created successfully');\n      refetchApiKeys();\n      setCreateApiKeyDialog(false);\n      setNewApiKeyName('');\n      setNewApiKeyPermissions([]);\n    },\n    onError: () => {\n      toast.error('Failed to create API key');\n    }\n  });\n  const revokeApiKeyMutation = useMutation(keyId => apiService.revokeApiKey(keyId), {\n    onSuccess: () => {\n      toast.success('API key revoked successfully');\n      refetchApiKeys();\n    },\n    onError: () => {\n      toast.error('Failed to revoke API key');\n    }\n  });\n  const handleSettingChange = (category, key, value) => {\n    setSettings(prev => ({\n      ...prev,\n      [category]: typeof prev[category] === 'object' && prev[category] !== null ? {\n        ...prev[category],\n        [key]: value\n      } : value\n    }));\n  };\n  const handleThemeChange = newTheme => {\n    setThemeMode(newTheme);\n    handleSettingChange('theme', '', newTheme);\n  };\n  const handleSaveSettings = () => {\n    updateSettingsMutation.mutate(settings);\n  };\n  const handleExportData = async () => {\n    setExportingData(true);\n    try {\n      const blob = await apiService.exportUserData();\n      const url = window.URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = `trustvault-data-${new Date().toISOString().split('T')[0]}.json`;\n      document.body.appendChild(a);\n      a.click();\n      window.URL.revokeObjectURL(url);\n      document.body.removeChild(a);\n      toast.success('Data exported successfully');\n    } catch (error) {\n      toast.error('Failed to export data');\n    } finally {\n      setExportingData(false);\n    }\n  };\n  const handleDeleteAccount = () => {\n    if (deleteConfirmation === 'DELETE') {\n      deleteAccountMutation.mutate(deleteConfirmation);\n    }\n  };\n  const timezones = [{\n    value: 'UTC',\n    label: 'UTC'\n  }, {\n    value: 'America/New_York',\n    label: 'Eastern Time'\n  }, {\n    value: 'America/Chicago',\n    label: 'Central Time'\n  }, {\n    value: 'America/Denver',\n    label: 'Mountain Time'\n  }, {\n    value: 'America/Los_Angeles',\n    label: 'Pacific Time'\n  }, {\n    value: 'Europe/London',\n    label: 'London'\n  }, {\n    value: 'Europe/Paris',\n    label: 'Paris'\n  }, {\n    value: 'Asia/Tokyo',\n    label: 'Tokyo'\n  }];\n  const languages = [{\n    value: 'en',\n    label: 'English'\n  }, {\n    value: 'fr',\n    label: 'Français'\n  }, {\n    value: 'es',\n    label: 'Español'\n  }, {\n    value: 'de',\n    label: 'Deutsch'\n  }];\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"Settings - TrustVault\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: \"Manage your account settings and preferences\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        mb: 4,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h1\",\n          gutterBottom: true,\n          children: \"Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          color: \"text.secondary\",\n          children: \"Manage your account preferences and security settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 2,\n                mb: 3,\n                children: [/*#__PURE__*/_jsxDEV(Settings, {\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: \"General\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                flexDirection: \"column\",\n                gap: 3,\n                children: [/*#__PURE__*/_jsxDEV(FormControl, {\n                  fullWidth: true,\n                  children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                    children: \"Timezone\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 328,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    value: settings.timezone,\n                    onChange: e => handleSettingChange('timezone', '', e.target.value),\n                    label: \"Timezone\",\n                    children: timezones.map(tz => /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: tz.value,\n                      children: tz.label\n                    }, tz.value, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 335,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                  fullWidth: true,\n                  children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                    children: \"Language\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 343,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    value: settings.language,\n                    onChange: e => handleSettingChange('language', '', e.target.value),\n                    label: \"Language\",\n                    children: languages.map(lang => /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: lang.value,\n                      children: lang.label\n                    }, lang.value, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 350,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 344,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                  fullWidth: true,\n                  children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                    children: \"Theme\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    value: themeMode,\n                    onChange: e => handleThemeChange(e.target.value),\n                    label: \"Theme\",\n                    children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"light\",\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: 1,\n                        children: [/*#__PURE__*/_jsxDEV(Brightness7, {\n                          fontSize: \"small\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 366,\n                          columnNumber: 27\n                        }, this), \"Light\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 365,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 364,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"dark\",\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: 1,\n                        children: [/*#__PURE__*/_jsxDEV(Brightness4, {\n                          fontSize: \"small\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 372,\n                          columnNumber: 27\n                        }, this), \"Dark\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 371,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 370,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"auto\",\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: 1,\n                        children: [/*#__PURE__*/_jsxDEV(BrightnessAuto, {\n                          fontSize: \"small\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 378,\n                          columnNumber: 27\n                        }, this), \"Auto (System)\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 377,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 376,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 2,\n                mb: 3,\n                children: [/*#__PURE__*/_jsxDEV(Notifications, {\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: \"Notifications\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                flexDirection: \"column\",\n                gap: 2,\n                mb: 3,\n                children: /*#__PURE__*/_jsxDEV(FormControl, {\n                  fullWidth: true,\n                  children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                    children: \"Email Frequency\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 400,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    value: settings.notifications.email_frequency,\n                    onChange: e => handleSettingChange('notifications', 'email_frequency', e.target.value),\n                    label: \"Email Frequency\",\n                    children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"immediate\",\n                      children: \"Immediate\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 406,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"daily\",\n                      children: \"Daily Digest\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 407,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"weekly\",\n                      children: \"Weekly Summary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 408,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"never\",\n                      children: \"Never\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 409,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 401,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(List, {\n                children: [/*#__PURE__*/_jsxDEV(ListItem, {\n                  children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: \"Email Notifications\",\n                    secondary: \"Receive updates via email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 416,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                    children: /*#__PURE__*/_jsxDEV(Switch, {\n                      checked: settings.notifications.email,\n                      onChange: e => handleSettingChange('notifications', 'email', e.target.checked)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 421,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 420,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                  children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: \"Push Notifications\",\n                    secondary: \"Browser push notifications\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 429,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                    children: /*#__PURE__*/_jsxDEV(Switch, {\n                      checked: settings.notifications.push,\n                      onChange: e => handleSettingChange('notifications', 'push', e.target.checked)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 434,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 433,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                  children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: \"Security Alerts\",\n                    secondary: \"Important security notifications (always enabled)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 442,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                    children: /*#__PURE__*/_jsxDEV(Switch, {\n                      checked: true,\n                      disabled: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 447,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 446,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                  children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: \"Portfolio Updates\",\n                    secondary: \"Portfolio performance notifications\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 455,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                    children: /*#__PURE__*/_jsxDEV(Switch, {\n                      checked: settings.notifications.portfolio,\n                      onChange: e => handleSettingChange('notifications', 'portfolio', e.target.checked)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 460,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 459,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                  children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: \"Marketing Communications\",\n                    secondary: \"Product updates and promotional content\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 468,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                    children: /*#__PURE__*/_jsxDEV(Switch, {\n                      checked: settings.notifications.marketing,\n                      onChange: e => handleSettingChange('notifications', 'marketing', e.target.checked)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 473,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 472,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 467,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                  children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: \"Market News\",\n                    secondary: \"Financial market news and insights\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 481,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                    children: /*#__PURE__*/_jsxDEV(Switch, {\n                      checked: settings.notifications.news,\n                      onChange: e => handleSettingChange('notifications', 'news', e.target.checked)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 486,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 485,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 480,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 2,\n                mb: 3,\n                children: [/*#__PURE__*/_jsxDEV(Security, {\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 502,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: \"Privacy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 503,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                flexDirection: \"column\",\n                gap: 3,\n                children: [/*#__PURE__*/_jsxDEV(FormControl, {\n                  fullWidth: true,\n                  children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                    children: \"Profile Visibility\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    value: settings.privacy.profile_visibility,\n                    onChange: e => handleSettingChange('privacy', 'profile_visibility', e.target.value),\n                    label: \"Profile Visibility\",\n                    children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"public\",\n                      children: \"Public\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 514,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"private\",\n                      children: \"Private\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 515,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 509,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 507,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                  fullWidth: true,\n                  children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                    children: \"Portfolio Visibility\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 520,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    value: settings.privacy.portfolio_visibility,\n                    onChange: e => handleSettingChange('privacy', 'portfolio_visibility', e.target.value),\n                    label: \"Portfolio Visibility\",\n                    children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"public\",\n                      children: \"Public\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 526,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"private\",\n                      children: \"Private\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 527,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 521,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 519,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {\n                sx: {\n                  my: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(List, {\n                children: [/*#__PURE__*/_jsxDEV(ListItem, {\n                  children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: \"Data Sharing\",\n                    secondary: \"Allow anonymized data sharing for research\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 536,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                    children: /*#__PURE__*/_jsxDEV(Switch, {\n                      checked: settings.privacy.data_sharing,\n                      onChange: e => handleSettingChange('privacy', 'data_sharing', e.target.checked)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 541,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 540,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                  children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: \"Analytics\",\n                    secondary: \"Help improve our service with usage analytics\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 549,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                    children: /*#__PURE__*/_jsxDEV(Switch, {\n                      checked: settings.privacy.analytics,\n                      onChange: e => handleSettingChange('privacy', 'analytics', e.target.checked)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 554,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 553,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 548,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 500,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 498,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 2,\n                mb: 3,\n                children: [/*#__PURE__*/_jsxDEV(Devices, {\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: \"Session Management\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 571,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                mb: 2,\n                children: \"Manage your active sessions and devices\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 574,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                flexDirection: \"column\",\n                gap: 2,\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  startIcon: /*#__PURE__*/_jsxDEV(Devices, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 581,\n                    columnNumber: 32\n                  }, this),\n                  onClick: () => setSessionsDialog(true),\n                  fullWidth: true,\n                  children: \"View Active Sessions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 579,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  color: \"warning\",\n                  onClick: () => revokeAllSessionsMutation.mutate(),\n                  disabled: revokeAllSessionsMutation.isLoading,\n                  fullWidth: true,\n                  children: revokeAllSessionsMutation.isLoading ? 'Revoking...' : 'Logout All Devices'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 578,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 566,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 2,\n                mb: 3,\n                children: [/*#__PURE__*/_jsxDEV(Download, {\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 607,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: \"Data Management\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 608,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                mb: 2,\n                children: \"Export your data or manage your account\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 611,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                flexDirection: \"column\",\n                gap: 2,\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  startIcon: /*#__PURE__*/_jsxDEV(Download, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 618,\n                    columnNumber: 32\n                  }, this),\n                  onClick: handleExportData,\n                  disabled: exportingData,\n                  fullWidth: true,\n                  children: exportingData ? 'Exporting...' : 'Export My Data'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 616,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 615,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 605,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 604,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 603,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 2,\n                mb: 3,\n                children: [/*#__PURE__*/_jsxDEV(Key, {\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 635,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: \"API Keys\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 636,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 634,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                mb: 2,\n                children: \"Manage API keys for third-party integrations\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 639,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                flexDirection: \"column\",\n                gap: 2,\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  startIcon: /*#__PURE__*/_jsxDEV(Key, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 646,\n                    columnNumber: 32\n                  }, this),\n                  onClick: () => setApiKeysDialog(true),\n                  fullWidth: true,\n                  children: \"Manage API Keys\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 644,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 643,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 632,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 631,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 2,\n                mb: 3,\n                children: [/*#__PURE__*/_jsxDEV(Delete, {\n                  color: \"error\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 662,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"error\",\n                  children: \"Danger Zone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 663,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 661,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Alert, {\n                severity: \"warning\",\n                sx: {\n                  mb: 2\n                },\n                children: \"These actions cannot be undone. Please be careful.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 666,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                color: \"error\",\n                startIcon: /*#__PURE__*/_jsxDEV(Delete, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 673,\n                  columnNumber: 30\n                }, this),\n                onClick: () => setDeleteAccountDialog(true),\n                fullWidth: true,\n                children: \"Delete Account\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 670,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 660,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 659,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 658,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        mt: 4,\n        display: \"flex\",\n        justifyContent: \"flex-end\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(Save, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 688,\n            columnNumber: 24\n          }, this),\n          onClick: handleSaveSettings,\n          disabled: updateSettingsMutation.isLoading,\n          children: updateSettingsMutation.isLoading ? 'Saving...' : 'Save Settings'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 686,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 685,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: deleteAccountDialog,\n        onClose: () => setDeleteAccountDialog(false),\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          color: \"error\",\n          children: \"Delete Account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 703,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"error\",\n            sx: {\n              mb: 2\n            },\n            children: \"This action will permanently delete your account and all associated data. This cannot be undone.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 705,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            gutterBottom: true,\n            children: \"To confirm, please type \\\"DELETE\\\" in the field below:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 710,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            value: deleteConfirmation,\n            onChange: e => setDeleteConfirmation(e.target.value),\n            placeholder: \"Type DELETE to confirm\",\n            sx: {\n              mt: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 714,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 704,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setDeleteAccountDialog(false),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 723,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"error\",\n            variant: \"contained\",\n            disabled: deleteConfirmation !== 'DELETE' || deleteAccountMutation.isLoading,\n            onClick: handleDeleteAccount,\n            startIcon: deleteAccountMutation.isLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 731,\n              columnNumber: 60\n            }, this) : /*#__PURE__*/_jsxDEV(Delete, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 731,\n              columnNumber: 93\n            }, this),\n            children: deleteAccountMutation.isLoading ? 'Deleting...' : 'Delete Account'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 726,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 722,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 697,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: sessionsDialog,\n        onClose: () => setSessionsDialog(false),\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Active Sessions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 745,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: activeSessions && activeSessions.length > 0 ? /*#__PURE__*/_jsxDEV(List, {\n            children: activeSessions.map(session => /*#__PURE__*/_jsxDEV(ListItem, {\n              divider: true,\n              children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body1\",\n                    children: session.device_name || 'Unknown Device'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 754,\n                    columnNumber: 27\n                  }, this), session.is_current && /*#__PURE__*/_jsxDEV(Chip, {\n                    label: \"Current\",\n                    color: \"primary\",\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 758,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 753,\n                  columnNumber: 25\n                }, this),\n                secondary: /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: [\"IP: \", session.ip_address]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 764,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: [\"Last active: \", new Date(session.last_activity).toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 767,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: [\"Location: \", session.location || 'Unknown']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 770,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 763,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 751,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                children: !session.is_current && /*#__PURE__*/_jsxDEV(Button, {\n                  color: \"error\",\n                  size: \"small\",\n                  onClick: () => revokeSessionMutation.mutate(session.id),\n                  disabled: revokeSessionMutation.isLoading,\n                  children: \"Revoke\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 778,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 776,\n                columnNumber: 21\n              }, this)]\n            }, session.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 750,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 748,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n            children: \"No active sessions found.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 792,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 746,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setSessionsDialog(false),\n            children: \"Close\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 796,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 795,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 739,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(SettingsPage, \"HnGx8tGpm5OsZ3omB2ZP8mXJ/k4=\", false, function () {\n  return [useAuthStore, useQueryClient, useTheme, useQuery, useQuery, useQuery, useMutation, useMutation, useMutation, useMutation, useMutation, useMutation];\n});\n_c = SettingsPage;\nexport default SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "FormControl", "InputLabel", "Select", "MenuItem", "Switch", "<PERSON><PERSON>", "<PERSON><PERSON>", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "CircularProgress", "Chip", "Divider", "Settings", "Notifications", "Security", "Delete", "Save", "Download", "Devices", "Key", "Brightness4", "Brightness7", "BrightnessAuto", "<PERSON><PERSON><PERSON>", "toast", "useMutation", "useQueryClient", "useQuery", "useAuthStore", "useTheme", "apiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SettingsPage", "_s", "user", "queryClient", "themeMode", "setThemeMode", "isDarkMode", "settings", "setSettings", "timezone", "language", "theme", "notifications", "email", "push", "security", "portfolio", "marketing", "news", "email_frequency", "push_enabled", "privacy", "profile_visibility", "portfolio_visibility", "data_sharing", "analytics", "deleteAccountDialog", "setDeleteAccountDialog", "deleteConfirmation", "setDeleteConfirmation", "sessionsDialog", "setSessionsDialog", "exportingData", "setExportingData", "apiKeysDialog", "setApiKeysDialog", "createApiKeyDialog", "setCreateApiKeyDialog", "newApiKeyName", "setNewApiKeyName", "newApiKeyPermissions", "setNewApiKeyPermissions", "data", "userSettings", "isLoading", "settingsLoading", "getUserSettings", "onSuccess", "prev", "onError", "activeSessions", "refetch", "refetchSessions", "getActiveSessions", "enabled", "error", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "refetchApiKeys", "getApi<PERSON>eys", "updateSettingsMutation", "updateUserSettings", "success", "invalidateQueries", "deleteAccountMutation", "confirmation", "deleteAccount", "window", "location", "href", "_error$response", "_error$response$data", "message", "response", "revokeSessionMutation", "sessionId", "revokeSession", "revokeAllSessionsMutation", "revokeAllSessions", "createApiKeyMutation", "name", "permissions", "createApiKey", "revokeApiKeyMutation", "keyId", "revokeApiKey", "handleSettingChange", "category", "key", "value", "handleThemeChange", "newTheme", "handleSaveSettings", "mutate", "handleExportData", "blob", "exportUserData", "url", "URL", "createObjectURL", "a", "document", "createElement", "download", "Date", "toISOString", "split", "body", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "handleDeleteAccount", "timezones", "label", "languages", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "mb", "variant", "component", "gutterBottom", "color", "container", "spacing", "item", "xs", "md", "display", "alignItems", "gap", "flexDirection", "fullWidth", "onChange", "e", "target", "map", "tz", "lang", "fontSize", "primary", "secondary", "checked", "disabled", "sx", "my", "startIcon", "onClick", "severity", "mt", "justifyContent", "open", "onClose", "max<PERSON><PERSON><PERSON>", "placeholder", "size", "length", "session", "divider", "device_name", "is_current", "ip_address", "last_activity", "toLocaleString", "id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/TrustVault/frontend/src/pages/Settings/SettingsPage.tsx"], "sourcesContent": ["// TrustVault - Settings Page\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Grid,\n  Card,\n  CardContent,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Switch,\n  Button,\n  Alert,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemSecondaryAction,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  CircularProgress,\n  Chip,\n  Divider,\n} from '@mui/material';\nimport {\n  Settings,\n  Notifications,\n  Security,\n  Delete,\n  Save,\n  Download,\n  Devices,\n  Key,\n  Brightness4,\n  Brightness7,\n  BrightnessAuto,\n} from '@mui/icons-material';\nimport { Helmet } from 'react-helmet-async';\nimport { toast } from 'react-hot-toast';\nimport { useMutation, useQueryClient, useQuery } from 'react-query';\n\n// Store\nimport { useAuthStore } from '../../store/authStore';\n\n// Contexts\nimport { useTheme } from '../../contexts/ThemeContext';\n\n// Services\nimport apiService from '../../services/api';\n\ninterface UserSettings {\n  timezone: string;\n  language: string;\n  theme: 'light' | 'dark' | 'auto';\n  notifications: {\n    email: boolean;\n    push: boolean;\n    security: boolean;\n    portfolio: boolean;\n    marketing: boolean;\n    news: boolean;\n    email_frequency: 'immediate' | 'daily' | 'weekly' | 'never';\n    push_enabled: boolean;\n  };\n  privacy: {\n    profile_visibility: 'public' | 'private';\n    portfolio_visibility: 'public' | 'private';\n    data_sharing: boolean;\n    analytics: boolean;\n  };\n}\n\nconst SettingsPage: React.FC = () => {\n  const { user } = useAuthStore();\n  const queryClient = useQueryClient();\n  const { themeMode, setThemeMode, isDarkMode } = useTheme();\n\n  const [settings, setSettings] = useState<UserSettings>({\n    timezone: user?.timezone || 'UTC',\n    language: user?.language || 'en',\n    theme: themeMode,\n    notifications: {\n      email: true,\n      push: true,\n      security: true,\n      portfolio: true,\n      marketing: false,\n      news: true,\n      email_frequency: 'daily',\n      push_enabled: true,\n    },\n    privacy: {\n      profile_visibility: 'private',\n      portfolio_visibility: 'private',\n      data_sharing: false,\n      analytics: true,\n    },\n  });\n\n  const [deleteAccountDialog, setDeleteAccountDialog] = useState(false);\n  const [deleteConfirmation, setDeleteConfirmation] = useState('');\n  const [sessionsDialog, setSessionsDialog] = useState(false);\n  const [exportingData, setExportingData] = useState(false);\n  const [apiKeysDialog, setApiKeysDialog] = useState(false);\n  const [createApiKeyDialog, setCreateApiKeyDialog] = useState(false);\n  const [newApiKeyName, setNewApiKeyName] = useState('');\n  const [newApiKeyPermissions, setNewApiKeyPermissions] = useState<string[]>([]);\n\n  // Load user settings\n  const { data: userSettings, isLoading: settingsLoading } = useQuery(\n    'user-settings',\n    apiService.getUserSettings,\n    {\n      onSuccess: (data) => {\n        setSettings(prev => ({ ...prev, ...data }));\n      },\n      onError: () => {\n        // Use default settings if API fails\n      },\n    }\n  );\n\n  // Load active sessions\n  const { data: activeSessions, refetch: refetchSessions } = useQuery(\n    'active-sessions',\n    apiService.getActiveSessions,\n    {\n      enabled: sessionsDialog,\n      onError: () => {\n        toast.error('Failed to load active sessions');\n      },\n    }\n  );\n\n  // Load API keys\n  const { data: apiKeys, refetch: refetchApiKeys } = useQuery(\n    'api-keys',\n    apiService.getApiKeys,\n    {\n      enabled: apiKeysDialog,\n      onError: () => {\n        toast.error('Failed to load API keys');\n      },\n    }\n  );\n\n  const updateSettingsMutation = useMutation(\n    (data: Partial<UserSettings>) => apiService.updateUserSettings(data),\n    {\n      onSuccess: () => {\n        toast.success('Settings updated successfully');\n        queryClient.invalidateQueries(['user-settings', 'user-profile']);\n      },\n      onError: () => {\n        toast.error('Failed to update settings');\n      },\n    }\n  );\n\n  const deleteAccountMutation = useMutation(\n    (confirmation: string) => apiService.deleteAccount(confirmation),\n    {\n      onSuccess: () => {\n        toast.success('Account deleted successfully');\n        // Redirect to login or home page\n        window.location.href = '/';\n      },\n      onError: (error: any) => {\n        const message = error.response?.data?.message || 'Failed to delete account';\n        toast.error(message);\n      },\n    }\n  );\n\n  const revokeSessionMutation = useMutation(\n    (sessionId: string) => apiService.revokeSession(sessionId),\n    {\n      onSuccess: () => {\n        toast.success('Session revoked successfully');\n        refetchSessions();\n      },\n      onError: () => {\n        toast.error('Failed to revoke session');\n      },\n    }\n  );\n\n  const revokeAllSessionsMutation = useMutation(\n    () => apiService.revokeAllSessions(),\n    {\n      onSuccess: () => {\n        toast.success('All sessions revoked successfully');\n        refetchSessions();\n      },\n      onError: () => {\n        toast.error('Failed to revoke all sessions');\n      },\n    }\n  );\n\n  const createApiKeyMutation = useMutation(\n    ({ name, permissions }: { name: string; permissions: string[] }) =>\n      apiService.createApiKey(name, permissions),\n    {\n      onSuccess: () => {\n        toast.success('API key created successfully');\n        refetchApiKeys();\n        setCreateApiKeyDialog(false);\n        setNewApiKeyName('');\n        setNewApiKeyPermissions([]);\n      },\n      onError: () => {\n        toast.error('Failed to create API key');\n      },\n    }\n  );\n\n  const revokeApiKeyMutation = useMutation(\n    (keyId: string) => apiService.revokeApiKey(keyId),\n    {\n      onSuccess: () => {\n        toast.success('API key revoked successfully');\n        refetchApiKeys();\n      },\n      onError: () => {\n        toast.error('Failed to revoke API key');\n      },\n    }\n  );\n\n  const handleSettingChange = (category: keyof UserSettings, key: string, value: any) => {\n    setSettings(prev => ({\n      ...prev,\n      [category]: typeof prev[category] === 'object' && prev[category] !== null\n        ? { ...(prev[category] as Record<string, any>), [key]: value }\n        : value\n    }));\n  };\n\n  const handleThemeChange = (newTheme: 'light' | 'dark' | 'auto') => {\n    setThemeMode(newTheme);\n    handleSettingChange('theme', '', newTheme);\n  };\n\n  const handleSaveSettings = () => {\n    updateSettingsMutation.mutate(settings);\n  };\n\n  const handleExportData = async () => {\n    setExportingData(true);\n    try {\n      const blob = await apiService.exportUserData();\n      const url = window.URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = `trustvault-data-${new Date().toISOString().split('T')[0]}.json`;\n      document.body.appendChild(a);\n      a.click();\n      window.URL.revokeObjectURL(url);\n      document.body.removeChild(a);\n      toast.success('Data exported successfully');\n    } catch (error) {\n      toast.error('Failed to export data');\n    } finally {\n      setExportingData(false);\n    }\n  };\n\n  const handleDeleteAccount = () => {\n    if (deleteConfirmation === 'DELETE') {\n      deleteAccountMutation.mutate(deleteConfirmation);\n    }\n  };\n\n  const timezones = [\n    { value: 'UTC', label: 'UTC' },\n    { value: 'America/New_York', label: 'Eastern Time' },\n    { value: 'America/Chicago', label: 'Central Time' },\n    { value: 'America/Denver', label: 'Mountain Time' },\n    { value: 'America/Los_Angeles', label: 'Pacific Time' },\n    { value: 'Europe/London', label: 'London' },\n    { value: 'Europe/Paris', label: 'Paris' },\n    { value: 'Asia/Tokyo', label: 'Tokyo' },\n  ];\n\n  const languages = [\n    { value: 'en', label: 'English' },\n    { value: 'fr', label: 'Français' },\n    { value: 'es', label: 'Español' },\n    { value: 'de', label: 'Deutsch' },\n  ];\n\n  return (\n    <>\n      <Helmet>\n        <title>Settings - TrustVault</title>\n        <meta name=\"description\" content=\"Manage your account settings and preferences\" />\n      </Helmet>\n\n      <Box>\n        {/* Header */}\n        <Box mb={4}>\n          <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n            Settings\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            Manage your account preferences and security settings\n          </Typography>\n        </Box>\n\n        <Grid container spacing={3}>\n          {/* General Settings */}\n          <Grid item xs={12} md={6}>\n            <Card>\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                  <Settings color=\"primary\" />\n                  <Typography variant=\"h6\">General</Typography>\n                </Box>\n\n                <Box display=\"flex\" flexDirection=\"column\" gap={3}>\n                  <FormControl fullWidth>\n                    <InputLabel>Timezone</InputLabel>\n                    <Select\n                      value={settings.timezone}\n                      onChange={(e) => handleSettingChange('timezone', '', e.target.value)}\n                      label=\"Timezone\"\n                    >\n                      {timezones.map((tz) => (\n                        <MenuItem key={tz.value} value={tz.value}>\n                          {tz.label}\n                        </MenuItem>\n                      ))}\n                    </Select>\n                  </FormControl>\n\n                  <FormControl fullWidth>\n                    <InputLabel>Language</InputLabel>\n                    <Select\n                      value={settings.language}\n                      onChange={(e) => handleSettingChange('language', '', e.target.value)}\n                      label=\"Language\"\n                    >\n                      {languages.map((lang) => (\n                        <MenuItem key={lang.value} value={lang.value}>\n                          {lang.label}\n                        </MenuItem>\n                      ))}\n                    </Select>\n                  </FormControl>\n\n                  <FormControl fullWidth>\n                    <InputLabel>Theme</InputLabel>\n                    <Select\n                      value={themeMode}\n                      onChange={(e) => handleThemeChange(e.target.value as 'light' | 'dark' | 'auto')}\n                      label=\"Theme\"\n                    >\n                      <MenuItem value=\"light\">\n                        <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                          <Brightness7 fontSize=\"small\" />\n                          Light\n                        </Box>\n                      </MenuItem>\n                      <MenuItem value=\"dark\">\n                        <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                          <Brightness4 fontSize=\"small\" />\n                          Dark\n                        </Box>\n                      </MenuItem>\n                      <MenuItem value=\"auto\">\n                        <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                          <BrightnessAuto fontSize=\"small\" />\n                          Auto (System)\n                        </Box>\n                      </MenuItem>\n                    </Select>\n                  </FormControl>\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n\n          {/* Notification Settings */}\n          <Grid item xs={12} md={6}>\n            <Card>\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                  <Notifications color=\"primary\" />\n                  <Typography variant=\"h6\">Notifications</Typography>\n                </Box>\n\n                <Box display=\"flex\" flexDirection=\"column\" gap={2} mb={3}>\n                  <FormControl fullWidth>\n                    <InputLabel>Email Frequency</InputLabel>\n                    <Select\n                      value={settings.notifications.email_frequency}\n                      onChange={(e) => handleSettingChange('notifications', 'email_frequency', e.target.value)}\n                      label=\"Email Frequency\"\n                    >\n                      <MenuItem value=\"immediate\">Immediate</MenuItem>\n                      <MenuItem value=\"daily\">Daily Digest</MenuItem>\n                      <MenuItem value=\"weekly\">Weekly Summary</MenuItem>\n                      <MenuItem value=\"never\">Never</MenuItem>\n                    </Select>\n                  </FormControl>\n                </Box>\n\n                <List>\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Email Notifications\"\n                      secondary=\"Receive updates via email\"\n                    />\n                    <ListItemSecondaryAction>\n                      <Switch\n                        checked={settings.notifications.email}\n                        onChange={(e) => handleSettingChange('notifications', 'email', e.target.checked)}\n                      />\n                    </ListItemSecondaryAction>\n                  </ListItem>\n\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Push Notifications\"\n                      secondary=\"Browser push notifications\"\n                    />\n                    <ListItemSecondaryAction>\n                      <Switch\n                        checked={settings.notifications.push}\n                        onChange={(e) => handleSettingChange('notifications', 'push', e.target.checked)}\n                      />\n                    </ListItemSecondaryAction>\n                  </ListItem>\n\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Security Alerts\"\n                      secondary=\"Important security notifications (always enabled)\"\n                    />\n                    <ListItemSecondaryAction>\n                      <Switch\n                        checked={true}\n                        disabled={true}\n                      />\n                    </ListItemSecondaryAction>\n                  </ListItem>\n\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Portfolio Updates\"\n                      secondary=\"Portfolio performance notifications\"\n                    />\n                    <ListItemSecondaryAction>\n                      <Switch\n                        checked={settings.notifications.portfolio}\n                        onChange={(e) => handleSettingChange('notifications', 'portfolio', e.target.checked)}\n                      />\n                    </ListItemSecondaryAction>\n                  </ListItem>\n\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Marketing Communications\"\n                      secondary=\"Product updates and promotional content\"\n                    />\n                    <ListItemSecondaryAction>\n                      <Switch\n                        checked={settings.notifications.marketing}\n                        onChange={(e) => handleSettingChange('notifications', 'marketing', e.target.checked)}\n                      />\n                    </ListItemSecondaryAction>\n                  </ListItem>\n\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Market News\"\n                      secondary=\"Financial market news and insights\"\n                    />\n                    <ListItemSecondaryAction>\n                      <Switch\n                        checked={settings.notifications.news}\n                        onChange={(e) => handleSettingChange('notifications', 'news', e.target.checked)}\n                      />\n                    </ListItemSecondaryAction>\n                  </ListItem>\n                </List>\n              </CardContent>\n            </Card>\n          </Grid>\n\n          {/* Privacy Settings */}\n          <Grid item xs={12} md={6}>\n            <Card>\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                  <Security color=\"primary\" />\n                  <Typography variant=\"h6\">Privacy</Typography>\n                </Box>\n\n                <Box display=\"flex\" flexDirection=\"column\" gap={3}>\n                  <FormControl fullWidth>\n                    <InputLabel>Profile Visibility</InputLabel>\n                    <Select\n                      value={settings.privacy.profile_visibility}\n                      onChange={(e) => handleSettingChange('privacy', 'profile_visibility', e.target.value)}\n                      label=\"Profile Visibility\"\n                    >\n                      <MenuItem value=\"public\">Public</MenuItem>\n                      <MenuItem value=\"private\">Private</MenuItem>\n                    </Select>\n                  </FormControl>\n\n                  <FormControl fullWidth>\n                    <InputLabel>Portfolio Visibility</InputLabel>\n                    <Select\n                      value={settings.privacy.portfolio_visibility}\n                      onChange={(e) => handleSettingChange('privacy', 'portfolio_visibility', e.target.value)}\n                      label=\"Portfolio Visibility\"\n                    >\n                      <MenuItem value=\"public\">Public</MenuItem>\n                      <MenuItem value=\"private\">Private</MenuItem>\n                    </Select>\n                  </FormControl>\n                </Box>\n\n                <Divider sx={{ my: 2 }} />\n\n                <List>\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Data Sharing\"\n                      secondary=\"Allow anonymized data sharing for research\"\n                    />\n                    <ListItemSecondaryAction>\n                      <Switch\n                        checked={settings.privacy.data_sharing}\n                        onChange={(e) => handleSettingChange('privacy', 'data_sharing', e.target.checked)}\n                      />\n                    </ListItemSecondaryAction>\n                  </ListItem>\n\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Analytics\"\n                      secondary=\"Help improve our service with usage analytics\"\n                    />\n                    <ListItemSecondaryAction>\n                      <Switch\n                        checked={settings.privacy.analytics}\n                        onChange={(e) => handleSettingChange('privacy', 'analytics', e.target.checked)}\n                      />\n                    </ListItemSecondaryAction>\n                  </ListItem>\n                </List>\n              </CardContent>\n            </Card>\n          </Grid>\n\n          {/* Session Management */}\n          <Grid item xs={12} md={6}>\n            <Card>\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                  <Devices color=\"primary\" />\n                  <Typography variant=\"h6\">Session Management</Typography>\n                </Box>\n\n                <Typography variant=\"body2\" color=\"text.secondary\" mb={2}>\n                  Manage your active sessions and devices\n                </Typography>\n\n                <Box display=\"flex\" flexDirection=\"column\" gap={2}>\n                  <Button\n                    variant=\"outlined\"\n                    startIcon={<Devices />}\n                    onClick={() => setSessionsDialog(true)}\n                    fullWidth\n                  >\n                    View Active Sessions\n                  </Button>\n\n                  <Button\n                    variant=\"outlined\"\n                    color=\"warning\"\n                    onClick={() => revokeAllSessionsMutation.mutate()}\n                    disabled={revokeAllSessionsMutation.isLoading}\n                    fullWidth\n                  >\n                    {revokeAllSessionsMutation.isLoading ? 'Revoking...' : 'Logout All Devices'}\n                  </Button>\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n\n          {/* Data Management */}\n          <Grid item xs={12} md={6}>\n            <Card>\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                  <Download color=\"primary\" />\n                  <Typography variant=\"h6\">Data Management</Typography>\n                </Box>\n\n                <Typography variant=\"body2\" color=\"text.secondary\" mb={2}>\n                  Export your data or manage your account\n                </Typography>\n\n                <Box display=\"flex\" flexDirection=\"column\" gap={2}>\n                  <Button\n                    variant=\"outlined\"\n                    startIcon={<Download />}\n                    onClick={handleExportData}\n                    disabled={exportingData}\n                    fullWidth\n                  >\n                    {exportingData ? 'Exporting...' : 'Export My Data'}\n                  </Button>\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n\n          {/* API Key Management */}\n          <Grid item xs={12} md={6}>\n            <Card>\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                  <Key color=\"primary\" />\n                  <Typography variant=\"h6\">API Keys</Typography>\n                </Box>\n\n                <Typography variant=\"body2\" color=\"text.secondary\" mb={2}>\n                  Manage API keys for third-party integrations\n                </Typography>\n\n                <Box display=\"flex\" flexDirection=\"column\" gap={2}>\n                  <Button\n                    variant=\"outlined\"\n                    startIcon={<Key />}\n                    onClick={() => setApiKeysDialog(true)}\n                    fullWidth\n                  >\n                    Manage API Keys\n                  </Button>\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n\n          {/* Danger Zone */}\n          <Grid item xs={12} md={6}>\n            <Card>\n              <CardContent>\n                <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                  <Delete color=\"error\" />\n                  <Typography variant=\"h6\" color=\"error\">Danger Zone</Typography>\n                </Box>\n\n                <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                  These actions cannot be undone. Please be careful.\n                </Alert>\n\n                <Button\n                  variant=\"outlined\"\n                  color=\"error\"\n                  startIcon={<Delete />}\n                  onClick={() => setDeleteAccountDialog(true)}\n                  fullWidth\n                >\n                  Delete Account\n                </Button>\n              </CardContent>\n            </Card>\n          </Grid>\n        </Grid>\n\n        {/* Save Button */}\n        <Box mt={4} display=\"flex\" justifyContent=\"flex-end\">\n          <Button\n            variant=\"contained\"\n            startIcon={<Save />}\n            onClick={handleSaveSettings}\n            disabled={updateSettingsMutation.isLoading}\n          >\n            {updateSettingsMutation.isLoading ? 'Saving...' : 'Save Settings'}\n          </Button>\n        </Box>\n\n        {/* Delete Account Dialog */}\n        <Dialog\n          open={deleteAccountDialog}\n          onClose={() => setDeleteAccountDialog(false)}\n          maxWidth=\"sm\"\n          fullWidth\n        >\n          <DialogTitle color=\"error\">Delete Account</DialogTitle>\n          <DialogContent>\n            <Alert severity=\"error\" sx={{ mb: 2 }}>\n              This action will permanently delete your account and all associated data.\n              This cannot be undone.\n            </Alert>\n            \n            <Typography variant=\"body2\" gutterBottom>\n              To confirm, please type \"DELETE\" in the field below:\n            </Typography>\n            \n            <TextField\n              fullWidth\n              value={deleteConfirmation}\n              onChange={(e) => setDeleteConfirmation(e.target.value)}\n              placeholder=\"Type DELETE to confirm\"\n              sx={{ mt: 2 }}\n            />\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={() => setDeleteAccountDialog(false)}>\n              Cancel\n            </Button>\n            <Button\n              color=\"error\"\n              variant=\"contained\"\n              disabled={deleteConfirmation !== 'DELETE' || deleteAccountMutation.isLoading}\n              onClick={handleDeleteAccount}\n              startIcon={deleteAccountMutation.isLoading ? <CircularProgress size={16} /> : <Delete />}\n            >\n              {deleteAccountMutation.isLoading ? 'Deleting...' : 'Delete Account'}\n            </Button>\n          </DialogActions>\n        </Dialog>\n\n        {/* Active Sessions Dialog */}\n        <Dialog\n          open={sessionsDialog}\n          onClose={() => setSessionsDialog(false)}\n          maxWidth=\"md\"\n          fullWidth\n        >\n          <DialogTitle>Active Sessions</DialogTitle>\n          <DialogContent>\n            {activeSessions && activeSessions.length > 0 ? (\n              <List>\n                {activeSessions.map((session: any) => (\n                  <ListItem key={session.id} divider>\n                    <ListItemText\n                      primary={\n                        <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                          <Typography variant=\"body1\">\n                            {session.device_name || 'Unknown Device'}\n                          </Typography>\n                          {session.is_current && (\n                            <Chip label=\"Current\" color=\"primary\" size=\"small\" />\n                          )}\n                        </Box>\n                      }\n                      secondary={\n                        <Box>\n                          <Typography variant=\"body2\" color=\"text.secondary\">\n                            IP: {session.ip_address}\n                          </Typography>\n                          <Typography variant=\"body2\" color=\"text.secondary\">\n                            Last active: {new Date(session.last_activity).toLocaleString()}\n                          </Typography>\n                          <Typography variant=\"body2\" color=\"text.secondary\">\n                            Location: {session.location || 'Unknown'}\n                          </Typography>\n                        </Box>\n                      }\n                    />\n                    <ListItemSecondaryAction>\n                      {!session.is_current && (\n                        <Button\n                          color=\"error\"\n                          size=\"small\"\n                          onClick={() => revokeSessionMutation.mutate(session.id)}\n                          disabled={revokeSessionMutation.isLoading}\n                        >\n                          Revoke\n                        </Button>\n                      )}\n                    </ListItemSecondaryAction>\n                  </ListItem>\n                ))}\n              </List>\n            ) : (\n              <Typography>No active sessions found.</Typography>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={() => setSessionsDialog(false)}>\n              Close\n            </Button>\n          </DialogActions>\n        </Dialog>\n      </Box>\n    </>\n  );\n};\n\nexport default SettingsPage;\n"], "mappings": ";;AAAA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,QAAmB,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,QACF,eAAe;AACtB,SACEC,QAAQ,EACRC,aAAa,EACbC,QAAQ,EACRC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,OAAO,EACPC,GAAG,EACHC,WAAW,EACXC,WAAW,EACXC,cAAc,QACT,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,KAAK,QAAQ,iBAAiB;AACvC,SAASC,WAAW,EAAEC,cAAc,EAAEC,QAAQ,QAAQ,aAAa;;AAEnE;AACA,SAASC,YAAY,QAAQ,uBAAuB;;AAEpD;AACA,SAASC,QAAQ,QAAQ,6BAA6B;;AAEtD;AACA,OAAOC,UAAU,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAwB5C,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM;IAAEC;EAAK,CAAC,GAAGT,YAAY,CAAC,CAAC;EAC/B,MAAMU,WAAW,GAAGZ,cAAc,CAAC,CAAC;EACpC,MAAM;IAAEa,SAAS;IAAEC,YAAY;IAAEC;EAAW,CAAC,GAAGZ,QAAQ,CAAC,CAAC;EAE1D,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGxD,QAAQ,CAAe;IACrDyD,QAAQ,EAAE,CAAAP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,QAAQ,KAAI,KAAK;IACjCC,QAAQ,EAAE,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,QAAQ,KAAI,IAAI;IAChCC,KAAK,EAAEP,SAAS;IAChBQ,aAAa,EAAE;MACbC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,KAAK;MAChBC,IAAI,EAAE,IAAI;MACVC,eAAe,EAAE,OAAO;MACxBC,YAAY,EAAE;IAChB,CAAC;IACDC,OAAO,EAAE;MACPC,kBAAkB,EAAE,SAAS;MAC7BC,oBAAoB,EAAE,SAAS;MAC/BC,YAAY,EAAE,KAAK;MACnBC,SAAS,EAAE;IACb;EACF,CAAC,CAAC;EAEF,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC4E,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAAC8E,cAAc,EAAEC,iBAAiB,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACgF,aAAa,EAAEC,gBAAgB,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACkF,aAAa,EAAEC,gBAAgB,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACoF,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACsF,aAAa,EAAEC,gBAAgB,CAAC,GAAGvF,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACwF,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGzF,QAAQ,CAAW,EAAE,CAAC;;EAE9E;EACA,MAAM;IAAE0F,IAAI,EAAEC,YAAY;IAAEC,SAAS,EAAEC;EAAgB,CAAC,GAAGrD,QAAQ,CACjE,eAAe,EACfG,UAAU,CAACmD,eAAe,EAC1B;IACEC,SAAS,EAAGL,IAAI,IAAK;MACnBlC,WAAW,CAACwC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,GAAGN;MAAK,CAAC,CAAC,CAAC;IAC7C,CAAC;IACDO,OAAO,EAAEA,CAAA,KAAM;MACb;IAAA;EAEJ,CACF,CAAC;;EAED;EACA,MAAM;IAAEP,IAAI,EAAEQ,cAAc;IAAEC,OAAO,EAAEC;EAAgB,CAAC,GAAG5D,QAAQ,CACjE,iBAAiB,EACjBG,UAAU,CAAC0D,iBAAiB,EAC5B;IACEC,OAAO,EAAExB,cAAc;IACvBmB,OAAO,EAAEA,CAAA,KAAM;MACb5D,KAAK,CAACkE,KAAK,CAAC,gCAAgC,CAAC;IAC/C;EACF,CACF,CAAC;;EAED;EACA,MAAM;IAAEb,IAAI,EAAEc,OAAO;IAAEL,OAAO,EAAEM;EAAe,CAAC,GAAGjE,QAAQ,CACzD,UAAU,EACVG,UAAU,CAAC+D,UAAU,EACrB;IACEJ,OAAO,EAAEpB,aAAa;IACtBe,OAAO,EAAEA,CAAA,KAAM;MACb5D,KAAK,CAACkE,KAAK,CAAC,yBAAyB,CAAC;IACxC;EACF,CACF,CAAC;EAED,MAAMI,sBAAsB,GAAGrE,WAAW,CACvCoD,IAA2B,IAAK/C,UAAU,CAACiE,kBAAkB,CAAClB,IAAI,CAAC,EACpE;IACEK,SAAS,EAAEA,CAAA,KAAM;MACf1D,KAAK,CAACwE,OAAO,CAAC,+BAA+B,CAAC;MAC9C1D,WAAW,CAAC2D,iBAAiB,CAAC,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC;IAClE,CAAC;IACDb,OAAO,EAAEA,CAAA,KAAM;MACb5D,KAAK,CAACkE,KAAK,CAAC,2BAA2B,CAAC;IAC1C;EACF,CACF,CAAC;EAED,MAAMQ,qBAAqB,GAAGzE,WAAW,CACtC0E,YAAoB,IAAKrE,UAAU,CAACsE,aAAa,CAACD,YAAY,CAAC,EAChE;IACEjB,SAAS,EAAEA,CAAA,KAAM;MACf1D,KAAK,CAACwE,OAAO,CAAC,8BAA8B,CAAC;MAC7C;MACAK,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;IAC5B,CAAC;IACDnB,OAAO,EAAGM,KAAU,IAAK;MAAA,IAAAc,eAAA,EAAAC,oBAAA;MACvB,MAAMC,OAAO,GAAG,EAAAF,eAAA,GAAAd,KAAK,CAACiB,QAAQ,cAAAH,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB3B,IAAI,cAAA4B,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,0BAA0B;MAC3ElF,KAAK,CAACkE,KAAK,CAACgB,OAAO,CAAC;IACtB;EACF,CACF,CAAC;EAED,MAAME,qBAAqB,GAAGnF,WAAW,CACtCoF,SAAiB,IAAK/E,UAAU,CAACgF,aAAa,CAACD,SAAS,CAAC,EAC1D;IACE3B,SAAS,EAAEA,CAAA,KAAM;MACf1D,KAAK,CAACwE,OAAO,CAAC,8BAA8B,CAAC;MAC7CT,eAAe,CAAC,CAAC;IACnB,CAAC;IACDH,OAAO,EAAEA,CAAA,KAAM;MACb5D,KAAK,CAACkE,KAAK,CAAC,0BAA0B,CAAC;IACzC;EACF,CACF,CAAC;EAED,MAAMqB,yBAAyB,GAAGtF,WAAW,CAC3C,MAAMK,UAAU,CAACkF,iBAAiB,CAAC,CAAC,EACpC;IACE9B,SAAS,EAAEA,CAAA,KAAM;MACf1D,KAAK,CAACwE,OAAO,CAAC,mCAAmC,CAAC;MAClDT,eAAe,CAAC,CAAC;IACnB,CAAC;IACDH,OAAO,EAAEA,CAAA,KAAM;MACb5D,KAAK,CAACkE,KAAK,CAAC,+BAA+B,CAAC;IAC9C;EACF,CACF,CAAC;EAED,MAAMuB,oBAAoB,GAAGxF,WAAW,CACtC,CAAC;IAAEyF,IAAI;IAAEC;EAAqD,CAAC,KAC7DrF,UAAU,CAACsF,YAAY,CAACF,IAAI,EAAEC,WAAW,CAAC,EAC5C;IACEjC,SAAS,EAAEA,CAAA,KAAM;MACf1D,KAAK,CAACwE,OAAO,CAAC,8BAA8B,CAAC;MAC7CJ,cAAc,CAAC,CAAC;MAChBpB,qBAAqB,CAAC,KAAK,CAAC;MAC5BE,gBAAgB,CAAC,EAAE,CAAC;MACpBE,uBAAuB,CAAC,EAAE,CAAC;IAC7B,CAAC;IACDQ,OAAO,EAAEA,CAAA,KAAM;MACb5D,KAAK,CAACkE,KAAK,CAAC,0BAA0B,CAAC;IACzC;EACF,CACF,CAAC;EAED,MAAM2B,oBAAoB,GAAG5F,WAAW,CACrC6F,KAAa,IAAKxF,UAAU,CAACyF,YAAY,CAACD,KAAK,CAAC,EACjD;IACEpC,SAAS,EAAEA,CAAA,KAAM;MACf1D,KAAK,CAACwE,OAAO,CAAC,8BAA8B,CAAC;MAC7CJ,cAAc,CAAC,CAAC;IAClB,CAAC;IACDR,OAAO,EAAEA,CAAA,KAAM;MACb5D,KAAK,CAACkE,KAAK,CAAC,0BAA0B,CAAC;IACzC;EACF,CACF,CAAC;EAED,MAAM8B,mBAAmB,GAAGA,CAACC,QAA4B,EAAEC,GAAW,EAAEC,KAAU,KAAK;IACrFhF,WAAW,CAACwC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACsC,QAAQ,GAAG,OAAOtC,IAAI,CAACsC,QAAQ,CAAC,KAAK,QAAQ,IAAItC,IAAI,CAACsC,QAAQ,CAAC,KAAK,IAAI,GACrE;QAAE,GAAItC,IAAI,CAACsC,QAAQ,CAAyB;QAAE,CAACC,GAAG,GAAGC;MAAM,CAAC,GAC5DA;IACN,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,iBAAiB,GAAIC,QAAmC,IAAK;IACjErF,YAAY,CAACqF,QAAQ,CAAC;IACtBL,mBAAmB,CAAC,OAAO,EAAE,EAAE,EAAEK,QAAQ,CAAC;EAC5C,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BhC,sBAAsB,CAACiC,MAAM,CAACrF,QAAQ,CAAC;EACzC,CAAC;EAED,MAAMsF,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC5D,gBAAgB,CAAC,IAAI,CAAC;IACtB,IAAI;MACF,MAAM6D,IAAI,GAAG,MAAMnG,UAAU,CAACoG,cAAc,CAAC,CAAC;MAC9C,MAAMC,GAAG,GAAG9B,MAAM,CAAC+B,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;MAC5C,MAAMK,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACrCF,CAAC,CAAC/B,IAAI,GAAG4B,GAAG;MACZG,CAAC,CAACG,QAAQ,GAAG,mBAAmB,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO;MAC7EL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,CAAC,CAAC;MAC5BA,CAAC,CAACS,KAAK,CAAC,CAAC;MACT1C,MAAM,CAAC+B,GAAG,CAACY,eAAe,CAACb,GAAG,CAAC;MAC/BI,QAAQ,CAACM,IAAI,CAACI,WAAW,CAACX,CAAC,CAAC;MAC5B9G,KAAK,CAACwE,OAAO,CAAC,4BAA4B,CAAC;IAC7C,CAAC,CAAC,OAAON,KAAK,EAAE;MACdlE,KAAK,CAACkE,KAAK,CAAC,uBAAuB,CAAC;IACtC,CAAC,SAAS;MACRtB,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAM8E,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAInF,kBAAkB,KAAK,QAAQ,EAAE;MACnCmC,qBAAqB,CAAC6B,MAAM,CAAChE,kBAAkB,CAAC;IAClD;EACF,CAAC;EAED,MAAMoF,SAAS,GAAG,CAChB;IAAExB,KAAK,EAAE,KAAK;IAAEyB,KAAK,EAAE;EAAM,CAAC,EAC9B;IAAEzB,KAAK,EAAE,kBAAkB;IAAEyB,KAAK,EAAE;EAAe,CAAC,EACpD;IAAEzB,KAAK,EAAE,iBAAiB;IAAEyB,KAAK,EAAE;EAAe,CAAC,EACnD;IAAEzB,KAAK,EAAE,gBAAgB;IAAEyB,KAAK,EAAE;EAAgB,CAAC,EACnD;IAAEzB,KAAK,EAAE,qBAAqB;IAAEyB,KAAK,EAAE;EAAe,CAAC,EACvD;IAAEzB,KAAK,EAAE,eAAe;IAAEyB,KAAK,EAAE;EAAS,CAAC,EAC3C;IAAEzB,KAAK,EAAE,cAAc;IAAEyB,KAAK,EAAE;EAAQ,CAAC,EACzC;IAAEzB,KAAK,EAAE,YAAY;IAAEyB,KAAK,EAAE;EAAQ,CAAC,CACxC;EAED,MAAMC,SAAS,GAAG,CAChB;IAAE1B,KAAK,EAAE,IAAI;IAAEyB,KAAK,EAAE;EAAU,CAAC,EACjC;IAAEzB,KAAK,EAAE,IAAI;IAAEyB,KAAK,EAAE;EAAW,CAAC,EAClC;IAAEzB,KAAK,EAAE,IAAI;IAAEyB,KAAK,EAAE;EAAU,CAAC,EACjC;IAAEzB,KAAK,EAAE,IAAI;IAAEyB,KAAK,EAAE;EAAU,CAAC,CAClC;EAED,oBACEpH,OAAA,CAAAE,SAAA;IAAAoH,QAAA,gBACEtH,OAAA,CAACT,MAAM;MAAA+H,QAAA,gBACLtH,OAAA;QAAAsH,QAAA,EAAO;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACpC1H,OAAA;QAAMkF,IAAI,EAAC,aAAa;QAACyC,OAAO,EAAC;MAA8C;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5E,CAAC,eAET1H,OAAA,CAAC5C,GAAG;MAAAkK,QAAA,gBAEFtH,OAAA,CAAC5C,GAAG;QAACwK,EAAE,EAAE,CAAE;QAAAN,QAAA,gBACTtH,OAAA,CAAC3C,UAAU;UAACwK,OAAO,EAAC,IAAI;UAACC,SAAS,EAAC,IAAI;UAACC,YAAY;UAAAT,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb1H,OAAA,CAAC3C,UAAU;UAACwK,OAAO,EAAC,OAAO;UAACG,KAAK,EAAC,gBAAgB;UAAAV,QAAA,EAAC;QAEnD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEN1H,OAAA,CAAC1C,IAAI;QAAC2K,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAZ,QAAA,gBAEzBtH,OAAA,CAAC1C,IAAI;UAAC6K,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAf,QAAA,eACvBtH,OAAA,CAACzC,IAAI;YAAA+J,QAAA,eACHtH,OAAA,CAACxC,WAAW;cAAA8J,QAAA,gBACVtH,OAAA,CAAC5C,GAAG;gBAACkL,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,GAAG,EAAE,CAAE;gBAACZ,EAAE,EAAE,CAAE;gBAAAN,QAAA,gBACpDtH,OAAA,CAACpB,QAAQ;kBAACoJ,KAAK,EAAC;gBAAS;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5B1H,OAAA,CAAC3C,UAAU;kBAACwK,OAAO,EAAC,IAAI;kBAAAP,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eAEN1H,OAAA,CAAC5C,GAAG;gBAACkL,OAAO,EAAC,MAAM;gBAACG,aAAa,EAAC,QAAQ;gBAACD,GAAG,EAAE,CAAE;gBAAAlB,QAAA,gBAChDtH,OAAA,CAACvC,WAAW;kBAACiL,SAAS;kBAAApB,QAAA,gBACpBtH,OAAA,CAACtC,UAAU;oBAAA4J,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACjC1H,OAAA,CAACrC,MAAM;oBACLgI,KAAK,EAAEjF,QAAQ,CAACE,QAAS;oBACzB+H,QAAQ,EAAGC,CAAC,IAAKpD,mBAAmB,CAAC,UAAU,EAAE,EAAE,EAAEoD,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAE;oBACrEyB,KAAK,EAAC,UAAU;oBAAAE,QAAA,EAEfH,SAAS,CAAC2B,GAAG,CAAEC,EAAE,iBAChB/I,OAAA,CAACpC,QAAQ;sBAAgB+H,KAAK,EAAEoD,EAAE,CAACpD,KAAM;sBAAA2B,QAAA,EACtCyB,EAAE,CAAC3B;oBAAK,GADI2B,EAAE,CAACpD,KAAK;sBAAA4B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEb,CACX;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAEd1H,OAAA,CAACvC,WAAW;kBAACiL,SAAS;kBAAApB,QAAA,gBACpBtH,OAAA,CAACtC,UAAU;oBAAA4J,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACjC1H,OAAA,CAACrC,MAAM;oBACLgI,KAAK,EAAEjF,QAAQ,CAACG,QAAS;oBACzB8H,QAAQ,EAAGC,CAAC,IAAKpD,mBAAmB,CAAC,UAAU,EAAE,EAAE,EAAEoD,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAE;oBACrEyB,KAAK,EAAC,UAAU;oBAAAE,QAAA,EAEfD,SAAS,CAACyB,GAAG,CAAEE,IAAI,iBAClBhJ,OAAA,CAACpC,QAAQ;sBAAkB+H,KAAK,EAAEqD,IAAI,CAACrD,KAAM;sBAAA2B,QAAA,EAC1C0B,IAAI,CAAC5B;oBAAK,GADE4B,IAAI,CAACrD,KAAK;sBAAA4B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEf,CACX;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAEd1H,OAAA,CAACvC,WAAW;kBAACiL,SAAS;kBAAApB,QAAA,gBACpBtH,OAAA,CAACtC,UAAU;oBAAA4J,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC9B1H,OAAA,CAACrC,MAAM;oBACLgI,KAAK,EAAEpF,SAAU;oBACjBoI,QAAQ,EAAGC,CAAC,IAAKhD,iBAAiB,CAACgD,CAAC,CAACC,MAAM,CAAClD,KAAkC,CAAE;oBAChFyB,KAAK,EAAC,OAAO;oBAAAE,QAAA,gBAEbtH,OAAA,CAACpC,QAAQ;sBAAC+H,KAAK,EAAC,OAAO;sBAAA2B,QAAA,eACrBtH,OAAA,CAAC5C,GAAG;wBAACkL,OAAO,EAAC,MAAM;wBAACC,UAAU,EAAC,QAAQ;wBAACC,GAAG,EAAE,CAAE;wBAAAlB,QAAA,gBAC7CtH,OAAA,CAACX,WAAW;0BAAC4J,QAAQ,EAAC;wBAAO;0BAAA1B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,SAElC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eACX1H,OAAA,CAACpC,QAAQ;sBAAC+H,KAAK,EAAC,MAAM;sBAAA2B,QAAA,eACpBtH,OAAA,CAAC5C,GAAG;wBAACkL,OAAO,EAAC,MAAM;wBAACC,UAAU,EAAC,QAAQ;wBAACC,GAAG,EAAE,CAAE;wBAAAlB,QAAA,gBAC7CtH,OAAA,CAACZ,WAAW;0BAAC6J,QAAQ,EAAC;wBAAO;0BAAA1B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,QAElC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eACX1H,OAAA,CAACpC,QAAQ;sBAAC+H,KAAK,EAAC,MAAM;sBAAA2B,QAAA,eACpBtH,OAAA,CAAC5C,GAAG;wBAACkL,OAAO,EAAC,MAAM;wBAACC,UAAU,EAAC,QAAQ;wBAACC,GAAG,EAAE,CAAE;wBAAAlB,QAAA,gBAC7CtH,OAAA,CAACV,cAAc;0BAAC2J,QAAQ,EAAC;wBAAO;0BAAA1B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,iBAErC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGP1H,OAAA,CAAC1C,IAAI;UAAC6K,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAf,QAAA,eACvBtH,OAAA,CAACzC,IAAI;YAAA+J,QAAA,eACHtH,OAAA,CAACxC,WAAW;cAAA8J,QAAA,gBACVtH,OAAA,CAAC5C,GAAG;gBAACkL,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,GAAG,EAAE,CAAE;gBAACZ,EAAE,EAAE,CAAE;gBAAAN,QAAA,gBACpDtH,OAAA,CAACnB,aAAa;kBAACmJ,KAAK,EAAC;gBAAS;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjC1H,OAAA,CAAC3C,UAAU;kBAACwK,OAAO,EAAC,IAAI;kBAAAP,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eAEN1H,OAAA,CAAC5C,GAAG;gBAACkL,OAAO,EAAC,MAAM;gBAACG,aAAa,EAAC,QAAQ;gBAACD,GAAG,EAAE,CAAE;gBAACZ,EAAE,EAAE,CAAE;gBAAAN,QAAA,eACvDtH,OAAA,CAACvC,WAAW;kBAACiL,SAAS;kBAAApB,QAAA,gBACpBtH,OAAA,CAACtC,UAAU;oBAAA4J,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACxC1H,OAAA,CAACrC,MAAM;oBACLgI,KAAK,EAAEjF,QAAQ,CAACK,aAAa,CAACO,eAAgB;oBAC9CqH,QAAQ,EAAGC,CAAC,IAAKpD,mBAAmB,CAAC,eAAe,EAAE,iBAAiB,EAAEoD,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAE;oBACzFyB,KAAK,EAAC,iBAAiB;oBAAAE,QAAA,gBAEvBtH,OAAA,CAACpC,QAAQ;sBAAC+H,KAAK,EAAC,WAAW;sBAAA2B,QAAA,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eAChD1H,OAAA,CAACpC,QAAQ;sBAAC+H,KAAK,EAAC,OAAO;sBAAA2B,QAAA,EAAC;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eAC/C1H,OAAA,CAACpC,QAAQ;sBAAC+H,KAAK,EAAC,QAAQ;sBAAA2B,QAAA,EAAC;oBAAc;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eAClD1H,OAAA,CAACpC,QAAQ;sBAAC+H,KAAK,EAAC,OAAO;sBAAA2B,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eAEN1H,OAAA,CAAChC,IAAI;gBAAAsJ,QAAA,gBACHtH,OAAA,CAAC/B,QAAQ;kBAAAqJ,QAAA,gBACPtH,OAAA,CAAC9B,YAAY;oBACXgL,OAAO,EAAC,qBAAqB;oBAC7BC,SAAS,EAAC;kBAA2B;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,eACF1H,OAAA,CAAC7B,uBAAuB;oBAAAmJ,QAAA,eACtBtH,OAAA,CAACnC,MAAM;sBACLuL,OAAO,EAAE1I,QAAQ,CAACK,aAAa,CAACC,KAAM;sBACtC2H,QAAQ,EAAGC,CAAC,IAAKpD,mBAAmB,CAAC,eAAe,EAAE,OAAO,EAAEoD,CAAC,CAACC,MAAM,CAACO,OAAO;oBAAE;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACqB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eAEX1H,OAAA,CAAC/B,QAAQ;kBAAAqJ,QAAA,gBACPtH,OAAA,CAAC9B,YAAY;oBACXgL,OAAO,EAAC,oBAAoB;oBAC5BC,SAAS,EAAC;kBAA4B;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC,eACF1H,OAAA,CAAC7B,uBAAuB;oBAAAmJ,QAAA,eACtBtH,OAAA,CAACnC,MAAM;sBACLuL,OAAO,EAAE1I,QAAQ,CAACK,aAAa,CAACE,IAAK;sBACrC0H,QAAQ,EAAGC,CAAC,IAAKpD,mBAAmB,CAAC,eAAe,EAAE,MAAM,EAAEoD,CAAC,CAACC,MAAM,CAACO,OAAO;oBAAE;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACqB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eAEX1H,OAAA,CAAC/B,QAAQ;kBAAAqJ,QAAA,gBACPtH,OAAA,CAAC9B,YAAY;oBACXgL,OAAO,EAAC,iBAAiB;oBACzBC,SAAS,EAAC;kBAAmD;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC,eACF1H,OAAA,CAAC7B,uBAAuB;oBAAAmJ,QAAA,eACtBtH,OAAA,CAACnC,MAAM;sBACLuL,OAAO,EAAE,IAAK;sBACdC,QAAQ,EAAE;oBAAK;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACqB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eAEX1H,OAAA,CAAC/B,QAAQ;kBAAAqJ,QAAA,gBACPtH,OAAA,CAAC9B,YAAY;oBACXgL,OAAO,EAAC,mBAAmB;oBAC3BC,SAAS,EAAC;kBAAqC;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC,eACF1H,OAAA,CAAC7B,uBAAuB;oBAAAmJ,QAAA,eACtBtH,OAAA,CAACnC,MAAM;sBACLuL,OAAO,EAAE1I,QAAQ,CAACK,aAAa,CAACI,SAAU;sBAC1CwH,QAAQ,EAAGC,CAAC,IAAKpD,mBAAmB,CAAC,eAAe,EAAE,WAAW,EAAEoD,CAAC,CAACC,MAAM,CAACO,OAAO;oBAAE;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACqB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eAEX1H,OAAA,CAAC/B,QAAQ;kBAAAqJ,QAAA,gBACPtH,OAAA,CAAC9B,YAAY;oBACXgL,OAAO,EAAC,0BAA0B;oBAClCC,SAAS,EAAC;kBAAyC;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC,eACF1H,OAAA,CAAC7B,uBAAuB;oBAAAmJ,QAAA,eACtBtH,OAAA,CAACnC,MAAM;sBACLuL,OAAO,EAAE1I,QAAQ,CAACK,aAAa,CAACK,SAAU;sBAC1CuH,QAAQ,EAAGC,CAAC,IAAKpD,mBAAmB,CAAC,eAAe,EAAE,WAAW,EAAEoD,CAAC,CAACC,MAAM,CAACO,OAAO;oBAAE;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACqB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eAEX1H,OAAA,CAAC/B,QAAQ;kBAAAqJ,QAAA,gBACPtH,OAAA,CAAC9B,YAAY;oBACXgL,OAAO,EAAC,aAAa;oBACrBC,SAAS,EAAC;kBAAoC;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC,eACF1H,OAAA,CAAC7B,uBAAuB;oBAAAmJ,QAAA,eACtBtH,OAAA,CAACnC,MAAM;sBACLuL,OAAO,EAAE1I,QAAQ,CAACK,aAAa,CAACM,IAAK;sBACrCsH,QAAQ,EAAGC,CAAC,IAAKpD,mBAAmB,CAAC,eAAe,EAAE,MAAM,EAAEoD,CAAC,CAACC,MAAM,CAACO,OAAO;oBAAE;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACqB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGP1H,OAAA,CAAC1C,IAAI;UAAC6K,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAf,QAAA,eACvBtH,OAAA,CAACzC,IAAI;YAAA+J,QAAA,eACHtH,OAAA,CAACxC,WAAW;cAAA8J,QAAA,gBACVtH,OAAA,CAAC5C,GAAG;gBAACkL,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,GAAG,EAAE,CAAE;gBAACZ,EAAE,EAAE,CAAE;gBAAAN,QAAA,gBACpDtH,OAAA,CAAClB,QAAQ;kBAACkJ,KAAK,EAAC;gBAAS;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5B1H,OAAA,CAAC3C,UAAU;kBAACwK,OAAO,EAAC,IAAI;kBAAAP,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eAEN1H,OAAA,CAAC5C,GAAG;gBAACkL,OAAO,EAAC,MAAM;gBAACG,aAAa,EAAC,QAAQ;gBAACD,GAAG,EAAE,CAAE;gBAAAlB,QAAA,gBAChDtH,OAAA,CAACvC,WAAW;kBAACiL,SAAS;kBAAApB,QAAA,gBACpBtH,OAAA,CAACtC,UAAU;oBAAA4J,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC3C1H,OAAA,CAACrC,MAAM;oBACLgI,KAAK,EAAEjF,QAAQ,CAACc,OAAO,CAACC,kBAAmB;oBAC3CkH,QAAQ,EAAGC,CAAC,IAAKpD,mBAAmB,CAAC,SAAS,EAAE,oBAAoB,EAAEoD,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAE;oBACtFyB,KAAK,EAAC,oBAAoB;oBAAAE,QAAA,gBAE1BtH,OAAA,CAACpC,QAAQ;sBAAC+H,KAAK,EAAC,QAAQ;sBAAA2B,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eAC1C1H,OAAA,CAACpC,QAAQ;sBAAC+H,KAAK,EAAC,SAAS;sBAAA2B,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAEd1H,OAAA,CAACvC,WAAW;kBAACiL,SAAS;kBAAApB,QAAA,gBACpBtH,OAAA,CAACtC,UAAU;oBAAA4J,QAAA,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC7C1H,OAAA,CAACrC,MAAM;oBACLgI,KAAK,EAAEjF,QAAQ,CAACc,OAAO,CAACE,oBAAqB;oBAC7CiH,QAAQ,EAAGC,CAAC,IAAKpD,mBAAmB,CAAC,SAAS,EAAE,sBAAsB,EAAEoD,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAE;oBACxFyB,KAAK,EAAC,sBAAsB;oBAAAE,QAAA,gBAE5BtH,OAAA,CAACpC,QAAQ;sBAAC+H,KAAK,EAAC,QAAQ;sBAAA2B,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eAC1C1H,OAAA,CAACpC,QAAQ;sBAAC+H,KAAK,EAAC,SAAS;sBAAA2B,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eAEN1H,OAAA,CAACrB,OAAO;gBAAC2K,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE;cAAE;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAE1B1H,OAAA,CAAChC,IAAI;gBAAAsJ,QAAA,gBACHtH,OAAA,CAAC/B,QAAQ;kBAAAqJ,QAAA,gBACPtH,OAAA,CAAC9B,YAAY;oBACXgL,OAAO,EAAC,cAAc;oBACtBC,SAAS,EAAC;kBAA4C;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC,eACF1H,OAAA,CAAC7B,uBAAuB;oBAAAmJ,QAAA,eACtBtH,OAAA,CAACnC,MAAM;sBACLuL,OAAO,EAAE1I,QAAQ,CAACc,OAAO,CAACG,YAAa;sBACvCgH,QAAQ,EAAGC,CAAC,IAAKpD,mBAAmB,CAAC,SAAS,EAAE,cAAc,EAAEoD,CAAC,CAACC,MAAM,CAACO,OAAO;oBAAE;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACqB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eAEX1H,OAAA,CAAC/B,QAAQ;kBAAAqJ,QAAA,gBACPtH,OAAA,CAAC9B,YAAY;oBACXgL,OAAO,EAAC,WAAW;oBACnBC,SAAS,EAAC;kBAA+C;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC,eACF1H,OAAA,CAAC7B,uBAAuB;oBAAAmJ,QAAA,eACtBtH,OAAA,CAACnC,MAAM;sBACLuL,OAAO,EAAE1I,QAAQ,CAACc,OAAO,CAACI,SAAU;sBACpC+G,QAAQ,EAAGC,CAAC,IAAKpD,mBAAmB,CAAC,SAAS,EAAE,WAAW,EAAEoD,CAAC,CAACC,MAAM,CAACO,OAAO;oBAAE;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACqB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGP1H,OAAA,CAAC1C,IAAI;UAAC6K,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAf,QAAA,eACvBtH,OAAA,CAACzC,IAAI;YAAA+J,QAAA,eACHtH,OAAA,CAACxC,WAAW;cAAA8J,QAAA,gBACVtH,OAAA,CAAC5C,GAAG;gBAACkL,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,GAAG,EAAE,CAAE;gBAACZ,EAAE,EAAE,CAAE;gBAAAN,QAAA,gBACpDtH,OAAA,CAACd,OAAO;kBAAC8I,KAAK,EAAC;gBAAS;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3B1H,OAAA,CAAC3C,UAAU;kBAACwK,OAAO,EAAC,IAAI;kBAAAP,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eAEN1H,OAAA,CAAC3C,UAAU;gBAACwK,OAAO,EAAC,OAAO;gBAACG,KAAK,EAAC,gBAAgB;gBAACJ,EAAE,EAAE,CAAE;gBAAAN,QAAA,EAAC;cAE1D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAEb1H,OAAA,CAAC5C,GAAG;gBAACkL,OAAO,EAAC,MAAM;gBAACG,aAAa,EAAC,QAAQ;gBAACD,GAAG,EAAE,CAAE;gBAAAlB,QAAA,gBAChDtH,OAAA,CAAClC,MAAM;kBACL+J,OAAO,EAAC,UAAU;kBAClB2B,SAAS,eAAExJ,OAAA,CAACd,OAAO;oBAAAqI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACvB+B,OAAO,EAAEA,CAAA,KAAMvH,iBAAiB,CAAC,IAAI,CAAE;kBACvCwG,SAAS;kBAAApB,QAAA,EACV;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAET1H,OAAA,CAAClC,MAAM;kBACL+J,OAAO,EAAC,UAAU;kBAClBG,KAAK,EAAC,SAAS;kBACfyB,OAAO,EAAEA,CAAA,KAAM1E,yBAAyB,CAACgB,MAAM,CAAC,CAAE;kBAClDsD,QAAQ,EAAEtE,yBAAyB,CAAChC,SAAU;kBAC9C2F,SAAS;kBAAApB,QAAA,EAERvC,yBAAyB,CAAChC,SAAS,GAAG,aAAa,GAAG;gBAAoB;kBAAAwE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGP1H,OAAA,CAAC1C,IAAI;UAAC6K,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAf,QAAA,eACvBtH,OAAA,CAACzC,IAAI;YAAA+J,QAAA,eACHtH,OAAA,CAACxC,WAAW;cAAA8J,QAAA,gBACVtH,OAAA,CAAC5C,GAAG;gBAACkL,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,GAAG,EAAE,CAAE;gBAACZ,EAAE,EAAE,CAAE;gBAAAN,QAAA,gBACpDtH,OAAA,CAACf,QAAQ;kBAAC+I,KAAK,EAAC;gBAAS;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5B1H,OAAA,CAAC3C,UAAU;kBAACwK,OAAO,EAAC,IAAI;kBAAAP,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eAEN1H,OAAA,CAAC3C,UAAU;gBAACwK,OAAO,EAAC,OAAO;gBAACG,KAAK,EAAC,gBAAgB;gBAACJ,EAAE,EAAE,CAAE;gBAAAN,QAAA,EAAC;cAE1D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAEb1H,OAAA,CAAC5C,GAAG;gBAACkL,OAAO,EAAC,MAAM;gBAACG,aAAa,EAAC,QAAQ;gBAACD,GAAG,EAAE,CAAE;gBAAAlB,QAAA,eAChDtH,OAAA,CAAClC,MAAM;kBACL+J,OAAO,EAAC,UAAU;kBAClB2B,SAAS,eAAExJ,OAAA,CAACf,QAAQ;oBAAAsI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACxB+B,OAAO,EAAEzD,gBAAiB;kBAC1BqD,QAAQ,EAAElH,aAAc;kBACxBuG,SAAS;kBAAApB,QAAA,EAERnF,aAAa,GAAG,cAAc,GAAG;gBAAgB;kBAAAoF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGP1H,OAAA,CAAC1C,IAAI;UAAC6K,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAf,QAAA,eACvBtH,OAAA,CAACzC,IAAI;YAAA+J,QAAA,eACHtH,OAAA,CAACxC,WAAW;cAAA8J,QAAA,gBACVtH,OAAA,CAAC5C,GAAG;gBAACkL,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,GAAG,EAAE,CAAE;gBAACZ,EAAE,EAAE,CAAE;gBAAAN,QAAA,gBACpDtH,OAAA,CAACb,GAAG;kBAAC6I,KAAK,EAAC;gBAAS;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvB1H,OAAA,CAAC3C,UAAU;kBAACwK,OAAO,EAAC,IAAI;kBAAAP,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eAEN1H,OAAA,CAAC3C,UAAU;gBAACwK,OAAO,EAAC,OAAO;gBAACG,KAAK,EAAC,gBAAgB;gBAACJ,EAAE,EAAE,CAAE;gBAAAN,QAAA,EAAC;cAE1D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAEb1H,OAAA,CAAC5C,GAAG;gBAACkL,OAAO,EAAC,MAAM;gBAACG,aAAa,EAAC,QAAQ;gBAACD,GAAG,EAAE,CAAE;gBAAAlB,QAAA,eAChDtH,OAAA,CAAClC,MAAM;kBACL+J,OAAO,EAAC,UAAU;kBAClB2B,SAAS,eAAExJ,OAAA,CAACb,GAAG;oBAAAoI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACnB+B,OAAO,EAAEA,CAAA,KAAMnH,gBAAgB,CAAC,IAAI,CAAE;kBACtCoG,SAAS;kBAAApB,QAAA,EACV;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGP1H,OAAA,CAAC1C,IAAI;UAAC6K,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAf,QAAA,eACvBtH,OAAA,CAACzC,IAAI;YAAA+J,QAAA,eACHtH,OAAA,CAACxC,WAAW;cAAA8J,QAAA,gBACVtH,OAAA,CAAC5C,GAAG;gBAACkL,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,GAAG,EAAE,CAAE;gBAACZ,EAAE,EAAE,CAAE;gBAAAN,QAAA,gBACpDtH,OAAA,CAACjB,MAAM;kBAACiJ,KAAK,EAAC;gBAAO;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxB1H,OAAA,CAAC3C,UAAU;kBAACwK,OAAO,EAAC,IAAI;kBAACG,KAAK,EAAC,OAAO;kBAAAV,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,eAEN1H,OAAA,CAACjC,KAAK;gBAAC2L,QAAQ,EAAC,SAAS;gBAACJ,EAAE,EAAE;kBAAE1B,EAAE,EAAE;gBAAE,CAAE;gBAAAN,QAAA,EAAC;cAEzC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAER1H,OAAA,CAAClC,MAAM;gBACL+J,OAAO,EAAC,UAAU;gBAClBG,KAAK,EAAC,OAAO;gBACbwB,SAAS,eAAExJ,OAAA,CAACjB,MAAM;kBAAAwI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACtB+B,OAAO,EAAEA,CAAA,KAAM3H,sBAAsB,CAAC,IAAI,CAAE;gBAC5C4G,SAAS;gBAAApB,QAAA,EACV;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGP1H,OAAA,CAAC5C,GAAG;QAACuM,EAAE,EAAE,CAAE;QAACrB,OAAO,EAAC,MAAM;QAACsB,cAAc,EAAC,UAAU;QAAAtC,QAAA,eAClDtH,OAAA,CAAClC,MAAM;UACL+J,OAAO,EAAC,WAAW;UACnB2B,SAAS,eAAExJ,OAAA,CAAChB,IAAI;YAAAuI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACpB+B,OAAO,EAAE3D,kBAAmB;UAC5BuD,QAAQ,EAAEvF,sBAAsB,CAACf,SAAU;UAAAuE,QAAA,EAE1CxD,sBAAsB,CAACf,SAAS,GAAG,WAAW,GAAG;QAAe;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGN1H,OAAA,CAAC5B,MAAM;QACLyL,IAAI,EAAEhI,mBAAoB;QAC1BiI,OAAO,EAAEA,CAAA,KAAMhI,sBAAsB,CAAC,KAAK,CAAE;QAC7CiI,QAAQ,EAAC,IAAI;QACbrB,SAAS;QAAApB,QAAA,gBAETtH,OAAA,CAAC3B,WAAW;UAAC2J,KAAK,EAAC,OAAO;UAAAV,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACvD1H,OAAA,CAAC1B,aAAa;UAAAgJ,QAAA,gBACZtH,OAAA,CAACjC,KAAK;YAAC2L,QAAQ,EAAC,OAAO;YAACJ,EAAE,EAAE;cAAE1B,EAAE,EAAE;YAAE,CAAE;YAAAN,QAAA,EAAC;UAGvC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAER1H,OAAA,CAAC3C,UAAU;YAACwK,OAAO,EAAC,OAAO;YAACE,YAAY;YAAAT,QAAA,EAAC;UAEzC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEb1H,OAAA,CAACxB,SAAS;YACRkK,SAAS;YACT/C,KAAK,EAAE5D,kBAAmB;YAC1B4G,QAAQ,EAAGC,CAAC,IAAK5G,qBAAqB,CAAC4G,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAE;YACvDqE,WAAW,EAAC,wBAAwB;YACpCV,EAAE,EAAE;cAAEK,EAAE,EAAE;YAAE;UAAE;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC,eAChB1H,OAAA,CAACzB,aAAa;UAAA+I,QAAA,gBACZtH,OAAA,CAAClC,MAAM;YAAC2L,OAAO,EAAEA,CAAA,KAAM3H,sBAAsB,CAAC,KAAK,CAAE;YAAAwF,QAAA,EAAC;UAEtD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT1H,OAAA,CAAClC,MAAM;YACLkK,KAAK,EAAC,OAAO;YACbH,OAAO,EAAC,WAAW;YACnBwB,QAAQ,EAAEtH,kBAAkB,KAAK,QAAQ,IAAImC,qBAAqB,CAACnB,SAAU;YAC7E0G,OAAO,EAAEvC,mBAAoB;YAC7BsC,SAAS,EAAEtF,qBAAqB,CAACnB,SAAS,gBAAG/C,OAAA,CAACvB,gBAAgB;cAACwL,IAAI,EAAE;YAAG;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG1H,OAAA,CAACjB,MAAM;cAAAwI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EAExFpD,qBAAqB,CAACnB,SAAS,GAAG,aAAa,GAAG;UAAgB;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGT1H,OAAA,CAAC5B,MAAM;QACLyL,IAAI,EAAE5H,cAAe;QACrB6H,OAAO,EAAEA,CAAA,KAAM5H,iBAAiB,CAAC,KAAK,CAAE;QACxC6H,QAAQ,EAAC,IAAI;QACbrB,SAAS;QAAApB,QAAA,gBAETtH,OAAA,CAAC3B,WAAW;UAAAiJ,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC1C1H,OAAA,CAAC1B,aAAa;UAAAgJ,QAAA,EACXjE,cAAc,IAAIA,cAAc,CAAC6G,MAAM,GAAG,CAAC,gBAC1ClK,OAAA,CAAChC,IAAI;YAAAsJ,QAAA,EACFjE,cAAc,CAACyF,GAAG,CAAEqB,OAAY,iBAC/BnK,OAAA,CAAC/B,QAAQ;cAAkBmM,OAAO;cAAA9C,QAAA,gBAChCtH,OAAA,CAAC9B,YAAY;gBACXgL,OAAO,eACLlJ,OAAA,CAAC5C,GAAG;kBAACkL,OAAO,EAAC,MAAM;kBAACC,UAAU,EAAC,QAAQ;kBAACC,GAAG,EAAE,CAAE;kBAAAlB,QAAA,gBAC7CtH,OAAA,CAAC3C,UAAU;oBAACwK,OAAO,EAAC,OAAO;oBAAAP,QAAA,EACxB6C,OAAO,CAACE,WAAW,IAAI;kBAAgB;oBAAA9C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,EACZyC,OAAO,CAACG,UAAU,iBACjBtK,OAAA,CAACtB,IAAI;oBAAC0I,KAAK,EAAC,SAAS;oBAACY,KAAK,EAAC,SAAS;oBAACiC,IAAI,EAAC;kBAAO;oBAAA1C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CACrD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;gBACDyB,SAAS,eACPnJ,OAAA,CAAC5C,GAAG;kBAAAkK,QAAA,gBACFtH,OAAA,CAAC3C,UAAU;oBAACwK,OAAO,EAAC,OAAO;oBAACG,KAAK,EAAC,gBAAgB;oBAAAV,QAAA,GAAC,MAC7C,EAAC6C,OAAO,CAACI,UAAU;kBAAA;oBAAAhD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC,eACb1H,OAAA,CAAC3C,UAAU;oBAACwK,OAAO,EAAC,OAAO;oBAACG,KAAK,EAAC,gBAAgB;oBAAAV,QAAA,GAAC,eACpC,EAAC,IAAIZ,IAAI,CAACyD,OAAO,CAACK,aAAa,CAAC,CAACC,cAAc,CAAC,CAAC;kBAAA;oBAAAlD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC,eACb1H,OAAA,CAAC3C,UAAU;oBAACwK,OAAO,EAAC,OAAO;oBAACG,KAAK,EAAC,gBAAgB;oBAAAV,QAAA,GAAC,YACvC,EAAC6C,OAAO,CAAC7F,QAAQ,IAAI,SAAS;kBAAA;oBAAAiD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACF1H,OAAA,CAAC7B,uBAAuB;gBAAAmJ,QAAA,EACrB,CAAC6C,OAAO,CAACG,UAAU,iBAClBtK,OAAA,CAAClC,MAAM;kBACLkK,KAAK,EAAC,OAAO;kBACbiC,IAAI,EAAC,OAAO;kBACZR,OAAO,EAAEA,CAAA,KAAM7E,qBAAqB,CAACmB,MAAM,CAACoE,OAAO,CAACO,EAAE,CAAE;kBACxDrB,QAAQ,EAAEzE,qBAAqB,CAAC7B,SAAU;kBAAAuE,QAAA,EAC3C;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cACT;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACsB,CAAC;YAAA,GArCbyC,OAAO,CAACO,EAAE;cAAAnD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsCf,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,gBAEP1H,OAAA,CAAC3C,UAAU;YAAAiK,QAAA,EAAC;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAClD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChB1H,OAAA,CAACzB,aAAa;UAAA+I,QAAA,eACZtH,OAAA,CAAClC,MAAM;YAAC2L,OAAO,EAAEA,CAAA,KAAMvH,iBAAiB,CAAC,KAAK,CAAE;YAAAoF,QAAA,EAAC;UAEjD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACtH,EAAA,CAttBID,YAAsB;EAAA,QACTP,YAAY,EACTF,cAAc,EACcG,QAAQ,EAkCGF,QAAQ,EAcRA,QAAQ,EAYhBA,QAAQ,EAW5BF,WAAW,EAaZA,WAAW,EAeXA,WAAW,EAaPA,WAAW,EAahBA,WAAW,EAiBXA,WAAW;AAAA;AAAAkL,EAAA,GAjJpCxK,YAAsB;AAwtB5B,eAAeA,YAAY;AAAC,IAAAwK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}