[{"C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\App.tsx": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\utils\\security.ts": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\store\\authStore.ts": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\NotFoundPage.tsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Auth\\LoginPage.tsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Auth\\RegisterPage.tsx": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Dashboard\\DashboardPage.tsx": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\PortfoliosPage.tsx": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\PortfolioDetailPage.tsx": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Security\\SecurityPage.tsx": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Profile\\ProfilePage.tsx": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\Auth\\ProtectedRoute.tsx": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\Layout\\Layout.tsx": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\services\\api.ts": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\CreatePortfolioPage.tsx": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\AddHoldingPage.tsx": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\TransactionsPage.tsx": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\AnalyticsPage.tsx": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Settings\\SettingsPage.tsx": "20", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\ErrorBoundary\\index.ts": "21", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\ErrorBoundary\\ErrorBoundary.tsx": "22", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\Profile\\ChangePasswordModal.tsx": "23", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\Profile\\MFASetupModal.tsx": "24"}, {"size": 2864, "mtime": 1753617893661, "results": "25", "hashOfConfig": "26"}, {"size": 4384, "mtime": 1753713773810, "results": "27", "hashOfConfig": "26"}, {"size": 8283, "mtime": 1753621069166, "results": "28", "hashOfConfig": "26"}, {"size": 5460, "mtime": 1753617989103, "results": "29", "hashOfConfig": "26"}, {"size": 1980, "mtime": 1753618351980, "results": "30", "hashOfConfig": "26"}, {"size": 6779, "mtime": 1753618095645, "results": "31", "hashOfConfig": "26"}, {"size": 14205, "mtime": 1753621205385, "results": "32", "hashOfConfig": "26"}, {"size": 11137, "mtime": 1753792308692, "results": "33", "hashOfConfig": "26"}, {"size": 6890, "mtime": 1753792319475, "results": "34", "hashOfConfig": "26"}, {"size": 10774, "mtime": 1753710814608, "results": "35", "hashOfConfig": "26"}, {"size": 12537, "mtime": 1753790756973, "results": "36", "hashOfConfig": "26"}, {"size": 12413, "mtime": 1753792976516, "results": "37", "hashOfConfig": "26"}, {"size": 1043, "mtime": 1753618067250, "results": "38", "hashOfConfig": "26"}, {"size": 7218, "mtime": 1753618170921, "results": "39", "hashOfConfig": "26"}, {"size": 10051, "mtime": 1753792722002, "results": "40", "hashOfConfig": "26"}, {"size": 8492, "mtime": 1753711056467, "results": "41", "hashOfConfig": "26"}, {"size": 16099, "mtime": 1753716215977, "results": "42", "hashOfConfig": "26"}, {"size": 11677, "mtime": 1753710882340, "results": "43", "hashOfConfig": "26"}, {"size": 14358, "mtime": 1753714848779, "results": "44", "hashOfConfig": "26"}, {"size": 13067, "mtime": 1753714889497, "results": "45", "hashOfConfig": "26"}, {"size": 118, "mtime": 1753713714543, "results": "46", "hashOfConfig": "26"}, {"size": 5770, "mtime": 1753713706774, "results": "47", "hashOfConfig": "26"}, {"size": 6901, "mtime": 1753792781745, "results": "48", "hashOfConfig": "26"}, {"size": 7501, "mtime": 1753792821431, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "15o5uiy", {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\utils\\security.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\store\\authStore.ts", ["122"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\NotFoundPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Auth\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Auth\\RegisterPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Dashboard\\DashboardPage.tsx", ["123"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\PortfoliosPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\PortfolioDetailPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Security\\SecurityPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Profile\\ProfilePage.tsx", ["124"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\Auth\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\Layout\\Layout.tsx", ["125"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\CreatePortfolioPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\AddHoldingPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\TransactionsPage.tsx", ["126", "127"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\AnalyticsPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Settings\\SettingsPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\ErrorBoundary\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\ErrorBoundary\\ErrorBoundary.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\Profile\\ChangePasswordModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\Profile\\MFASetupModal.tsx", ["128", "129", "130"], [], {"ruleId": "131", "severity": 1, "message": "132", "line": 6, "column": 16, "nodeType": "133", "messageId": "134", "endLine": 6, "endColumn": 26}, {"ruleId": "131", "severity": 1, "message": "135", "line": 10, "column": 3, "nodeType": "133", "messageId": "134", "endLine": 10, "endColumn": 8}, {"ruleId": "131", "severity": 1, "message": "136", "line": 39, "column": 10, "nodeType": "133", "messageId": "134", "endLine": 39, "endColumn": 14}, {"ruleId": "131", "severity": 1, "message": "137", "line": 33, "column": 3, "nodeType": "133", "messageId": "134", "endLine": 33, "endColumn": 14}, {"ruleId": "131", "severity": 1, "message": "138", "line": 31, "column": 3, "nodeType": "133", "messageId": "134", "endLine": 31, "endColumn": 13}, {"ruleId": "131", "severity": 1, "message": "139", "line": 45, "column": 10, "nodeType": "133", "messageId": "134", "endLine": 45, "endColumn": 29}, {"ruleId": "131", "severity": 1, "message": "140", "line": 25, "column": 3, "nodeType": "133", "messageId": "134", "endLine": 25, "endColumn": 9}, {"ruleId": "131", "severity": 1, "message": "141", "line": 26, "column": 3, "nodeType": "133", "messageId": "134", "endLine": 26, "endColumn": 6}, {"ruleId": "142", "severity": 1, "message": "143", "line": 140, "column": 6, "nodeType": "144", "endLine": 140, "endColumn": 12, "suggestions": "145"}, "@typescript-eslint/no-unused-vars", "'AuthTokens' is defined but never used.", "Identifier", "unusedVar", "'Paper' is defined but never used.", "'User' is defined but never used.", "'ChevronLeft' is defined but never used.", "'FilterList' is defined but never used.", "'selectedTransaction' is assigned a value but never used.", "'QrCode' is defined but never used.", "'Key' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'activeStep'. Either include it or remove the dependency array.", "ArrayExpression", ["146"], {"desc": "147", "fix": "148"}, "Update the dependencies array to be: [activeStep, open]", {"range": "149", "text": "150"}, [3157, 3163], "[activeStep, open]"]