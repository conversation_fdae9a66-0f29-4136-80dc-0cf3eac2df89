[{"C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\App.tsx": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\utils\\security.ts": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\store\\authStore.ts": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\NotFoundPage.tsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Auth\\LoginPage.tsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Auth\\RegisterPage.tsx": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Dashboard\\DashboardPage.tsx": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\PortfoliosPage.tsx": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\PortfolioDetailPage.tsx": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Security\\SecurityPage.tsx": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Profile\\ProfilePage.tsx": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\Auth\\ProtectedRoute.tsx": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\Layout\\Layout.tsx": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\services\\api.ts": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\CreatePortfolioPage.tsx": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\AddHoldingPage.tsx": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\TransactionsPage.tsx": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\AnalyticsPage.tsx": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Settings\\SettingsPage.tsx": "20", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\ErrorBoundary\\index.ts": "21", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\ErrorBoundary\\ErrorBoundary.tsx": "22", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\Profile\\ChangePasswordModal.tsx": "23", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\Profile\\MFASetupModal.tsx": "24", "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\contexts\\ThemeContext.tsx": "25"}, {"size": 2864, "mtime": 1753617893661, "results": "26", "hashOfConfig": "27"}, {"size": 4517, "mtime": 1753793332026, "results": "28", "hashOfConfig": "27"}, {"size": 8283, "mtime": 1753621069166, "results": "29", "hashOfConfig": "27"}, {"size": 5460, "mtime": 1753617989103, "results": "30", "hashOfConfig": "27"}, {"size": 1980, "mtime": 1753618351980, "results": "31", "hashOfConfig": "27"}, {"size": 6779, "mtime": 1753618095645, "results": "32", "hashOfConfig": "27"}, {"size": 14205, "mtime": 1753621205385, "results": "33", "hashOfConfig": "27"}, {"size": 11137, "mtime": 1753792308692, "results": "34", "hashOfConfig": "27"}, {"size": 6890, "mtime": 1753792319475, "results": "35", "hashOfConfig": "27"}, {"size": 10774, "mtime": 1753710814608, "results": "36", "hashOfConfig": "27"}, {"size": 12537, "mtime": 1753790756973, "results": "37", "hashOfConfig": "27"}, {"size": 12413, "mtime": 1753792976516, "results": "38", "hashOfConfig": "27"}, {"size": 1043, "mtime": 1753618067250, "results": "39", "hashOfConfig": "27"}, {"size": 7218, "mtime": 1753618170921, "results": "40", "hashOfConfig": "27"}, {"size": 11838, "mtime": 1753793621838, "results": "41", "hashOfConfig": "27"}, {"size": 8492, "mtime": 1753711056467, "results": "42", "hashOfConfig": "27"}, {"size": 16099, "mtime": 1753716215977, "results": "43", "hashOfConfig": "27"}, {"size": 11677, "mtime": 1753710882340, "results": "44", "hashOfConfig": "27"}, {"size": 14358, "mtime": 1753714848779, "results": "45", "hashOfConfig": "27"}, {"size": 32393, "mtime": 1753793706978, "results": "46", "hashOfConfig": "27"}, {"size": 118, "mtime": 1753713714543, "results": "47", "hashOfConfig": "27"}, {"size": 5770, "mtime": 1753713706774, "results": "48", "hashOfConfig": "27"}, {"size": 6901, "mtime": 1753792781745, "results": "49", "hashOfConfig": "27"}, {"size": 7501, "mtime": 1753792821431, "results": "50", "hashOfConfig": "27"}, {"size": 4000, "mtime": 1753793283898, "results": "51", "hashOfConfig": "27"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "15o5uiy", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\utils\\security.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\store\\authStore.ts", ["127"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\NotFoundPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Auth\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Auth\\RegisterPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Dashboard\\DashboardPage.tsx", ["128"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\PortfoliosPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\PortfolioDetailPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Security\\SecurityPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Profile\\ProfilePage.tsx", ["129"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\Auth\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\Layout\\Layout.tsx", ["130"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\CreatePortfolioPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\AddHoldingPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\TransactionsPage.tsx", ["131", "132"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Portfolio\\AnalyticsPage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\pages\\Settings\\SettingsPage.tsx", ["133", "134", "135", "136"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\ErrorBoundary\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\ErrorBoundary\\ErrorBoundary.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\Profile\\ChangePasswordModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\components\\Profile\\MFASetupModal.tsx", ["137", "138", "139"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TrustVault\\frontend\\src\\contexts\\ThemeContext.tsx", [], [], {"ruleId": "140", "severity": 1, "message": "141", "line": 6, "column": 16, "nodeType": "142", "messageId": "143", "endLine": 6, "endColumn": 26}, {"ruleId": "140", "severity": 1, "message": "144", "line": 10, "column": 3, "nodeType": "142", "messageId": "143", "endLine": 10, "endColumn": 8}, {"ruleId": "140", "severity": 1, "message": "145", "line": 39, "column": 10, "nodeType": "142", "messageId": "143", "endLine": 39, "endColumn": 14}, {"ruleId": "140", "severity": 1, "message": "146", "line": 33, "column": 3, "nodeType": "142", "messageId": "143", "endLine": 33, "endColumn": 14}, {"ruleId": "140", "severity": 1, "message": "147", "line": 31, "column": 3, "nodeType": "142", "messageId": "143", "endLine": 31, "endColumn": 13}, {"ruleId": "140", "severity": 1, "message": "148", "line": 45, "column": 10, "nodeType": "142", "messageId": "143", "endLine": 45, "endColumn": 29}, {"ruleId": "140", "severity": 1, "message": "149", "line": 3, "column": 27, "nodeType": "142", "messageId": "143", "endLine": 3, "endColumn": 36}, {"ruleId": "140", "severity": 1, "message": "150", "line": 81, "column": 36, "nodeType": "142", "messageId": "143", "endLine": 81, "endColumn": 46}, {"ruleId": "140", "severity": 1, "message": "151", "line": 115, "column": 17, "nodeType": "142", "messageId": "143", "endLine": 115, "endColumn": 29}, {"ruleId": "140", "severity": 1, "message": "152", "line": 115, "column": 42, "nodeType": "142", "messageId": "143", "endLine": 115, "endColumn": 57}, {"ruleId": "140", "severity": 1, "message": "153", "line": 25, "column": 3, "nodeType": "142", "messageId": "143", "endLine": 25, "endColumn": 9}, {"ruleId": "140", "severity": 1, "message": "154", "line": 26, "column": 3, "nodeType": "142", "messageId": "143", "endLine": 26, "endColumn": 6}, {"ruleId": "155", "severity": 1, "message": "156", "line": 140, "column": 6, "nodeType": "157", "endLine": 140, "endColumn": 12, "suggestions": "158"}, "@typescript-eslint/no-unused-vars", "'AuthTokens' is defined but never used.", "Identifier", "unusedVar", "'Paper' is defined but never used.", "'User' is defined but never used.", "'ChevronLeft' is defined but never used.", "'FilterList' is defined but never used.", "'selectedTransaction' is assigned a value but never used.", "'useEffect' is defined but never used.", "'isDarkMode' is assigned a value but never used.", "'userSettings' is assigned a value but never used.", "'settingsLoading' is assigned a value but never used.", "'QrCode' is defined but never used.", "'Key' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'activeStep'. Either include it or remove the dependency array.", "ArrayExpression", ["159"], {"desc": "160", "fix": "161"}, "Update the dependencies array to be: [activeStep, open]", {"range": "162", "text": "163"}, [3157, 3163], "[activeStep, open]"]